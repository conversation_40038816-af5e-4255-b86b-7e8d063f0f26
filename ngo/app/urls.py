from django.urls import path
from . import views

urlpatterns = [
    path('', views.home, name='home'),
    path('about/', views.about, name='about'),
    path('programs/', views.programs, name='programs'),
    path('programs/<int:program_id>/', views.program_detail, name='program_detail'),
    path('projects/', views.projects, name='projects'),
    path('projects/<int:project_id>/', views.project_detail, name='project_detail'),
    path('team/', views.team, name='team'),
    path('impact/', views.impact, name='impact'),
    path('resources/', views.resources, name='resources'),
    path('contact/', views.contact, name='contact'),
    path('gallery/', views.gallery, name='gallery'),
    path('gallery/<int:gallery_id>/', views.gallery_detail, name='gallery_detail'),
    path('donate/', views.donate, name='donate'),
]

