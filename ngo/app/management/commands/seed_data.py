from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, date
from decimal import Decimal
from app.models import *

class Command(BaseCommand):
    help = 'Seed database with YARCN NGO data from the profile'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Starting data seeding...'))

        # Create Organization
        organization, created = Organization.objects.get_or_create(
            name="Youth Awareness Raising Center Nepal (YARCN) Jajarkot",
            defaults={
                'tagline': "Empowering youth for socio-economic transformation",
                'address': "Bheri Municipality 4 Jajarkot",
                'phone': "089-430167",
                'email': "<EMAIL>",
                'website': "www.yarcn.org",
                'established_date': date(2003, 2, 14),
                'vision': "We are formulating a prosperous Nepali society with self-reliant, improved, and sustained quality of life and dignity.",
                'mission': "Youth Awareness Raising Centre Nepal is committed to creating youth solidarity and their collective efforts for socio-economic transformation, ascertainment of fundamental rights, and self-reliance of the target population - women, Dalit, minorities, differently-abled people, other disadvantaged and ultra-poor people. YARCN believes in human potentiality, people's participation, and resource sharing.",
                'goal': "To create youth synergy and solidarity for Socio-economic transformation."
            }
        )
        
        if created:
            self.stdout.write(self.style.SUCCESS('Organization created'))
        else:
            self.stdout.write(self.style.WARNING('Organization already exists'))

        # Create Core Values
        core_values_data = [
            {
                'title': 'Mutual Respect',
                'description': 'YARCN respects the diversity, dignity, uniqueness, and intrinsic worth of every Individual as an equal human being, irrespective of Gender, Ethnicity, and religion.',
                'icon': 'handshake'
            },
            {
                'title': 'Commitment',
                'description': 'YARCN is committed to being accountable for its collective and individual actions and acting consistently with its vision and complete integrity.',
                'icon': 'heart'
            },
            {
                'title': 'Quality of Work',
                'description': 'YARCN ensures the quality of work through participatory monitoring and evaluation (M/E), social auditing, public hearings, and adopting standard guidelines/protocols.',
                'icon': 'award'
            },
            {
                'title': 'Transparency',
                'description': 'To ensure good governance in its operations, YARCN will institutionalize and operationalize social auditing and transparent accountability procedures and systems.',
                'icon': 'eye'
            },
            {
                'title': 'Professional',
                'description': 'YARCN will seek to conduct itself professionally, independently, nonpolitically, and nonpartisanally by embracing business principles in the development and execution of its services.',
                'icon': 'briefcase'
            }
        ]

        for value_data in core_values_data:
            value, created = CoreValue.objects.get_or_create(
                title=value_data['title'],
                defaults=value_data
            )
            if created:
                self.stdout.write(f'Core value "{value_data["title"]}" created')

        # Create Registration Records
        registrations_data = [
            {
                'office_name': 'Administration Office Kathmandu',
                'registration_date': date(2003, 3, 12),
                'registration_number': '561'
            },
            {
                'office_name': 'Social Welfare Council, Kathmandu',
                'registration_date': date(2003, 4, 16),
                'registration_number': '14662'
            },
            {
                'office_name': 'NGO. fed Nepal, Jajarkot',
                'registration_date': date(2004, 2, 20),
                'registration_number': 'MMR-Jajarkot-14'
            },
            {
                'office_name': 'Internal Revenue Office Kathmandu',
                'registration_date': date(2004, 11, 4),
                'registration_number': '301763875'
            }
        ]

        for reg_data in registrations_data:
            registration, created = Registration.objects.get_or_create(
                office_name=reg_data['office_name'],
                defaults=reg_data
            )
            if created:
                self.stdout.write(f'Registration "{reg_data["office_name"]}" created')

        # Create Program Components
        programs_data = [
            {
                'title': 'Promoting Food Security and Sustainable Livelihood',
                'description': 'Programs focused on improving food security and creating sustainable livelihood opportunities for marginalized communities.',
                'icon': 'seedling',
                'order': 1
            },
            {
                'title': 'Social Service Strengthening: Health, WASH and Education',
                'description': 'Comprehensive programs addressing health, water sanitation hygiene, and educational needs of communities.',
                'icon': 'hospital',
                'order': 2
            },
            {
                'title': 'Community Infrastructure Building',
                'description': 'Development of essential infrastructure to support community growth and development.',
                'icon': 'hammer',
                'order': 3
            },
            {
                'title': 'Governance Strengthening',
                'description': 'Programs to enhance local governance and democratic participation in communities.',
                'icon': 'landmark',
                'order': 4
            },
            {
                'title': 'Indigenous Knowledge Promotion',
                'description': 'Preserving and promoting traditional knowledge and practices of indigenous communities.',
                'icon': 'book',
                'order': 5
            },
            {
                'title': 'Disaster Response and Climate Change Adaptation',
                'description': 'Emergency response and building resilience against natural disasters and climate change.',
                'icon': 'shield-alt',
                'order': 6
            },
            {
                'title': 'Conflict Resolution and Peace Building',
                'description': 'Programs focused on conflict resolution and building sustainable peace in communities.',
                'icon': 'dove',
                'order': 7
            },
            {
                'title': 'Child Protection and child-friendly local government',
                'description': 'Ensuring child rights and protection through community networks and local government engagement.',
                'icon': 'child',
                'order': 8
            },
            {
                'title': 'Youth Awareness and Mobilization',
                'description': 'Empowering youth through awareness campaigns and community mobilization activities.',
                'icon': 'users',
                'order': 9
            },
            {
                'title': 'Income generation program',
                'description': 'Creating economic opportunities and supporting entrepreneurship in marginalized communities.',
                'icon': 'coins',
                'order': 10
            },
            {
                'title': 'Earthquake Emergency Response Program',
                'description': 'Specialized response programs for earthquake-affected communities with shelter and recovery support.',
                'icon': 'home',
                'order': 11
            }
        ]

        for program_data in programs_data:
            program, created = ProgramComponent.objects.get_or_create(
                title=program_data['title'],
                defaults=program_data
            )
            if created:
                self.stdout.write(f'Program "{program_data["title"]}" created')

        # Create Major Projects
        projects_data = [
            {
                'title': 'Jajarkot Earthquake Response and Recovery Project',
                'donor_agency': 'Caritas Germany, Misereor, CRS, Caritas Austria, Caritas Australia',
                'start_date': date(2023, 11, 1),
                'end_date': date(2024, 3, 31),
                'contract_amount': Decimal('6679994'),
                'beneficiaries_count': 1500,
                'location': 'Bheri Municipality 1,2,3,4,12 and Nalgaad Municipality 1,3,4,5 of Jajarkot and Aathbiskot Municipality 11,13 and 14 of Rukum West',
                'core_activities': 'Immediate relief materials distribution, temporary shelters construction, temporary learning centers establishment, socio-technical support, IEC initiatives, PSA dissemination, psychosocial counseling services.',
                'status': 'completed'
            },
            {
                'title': 'Strengthening Urban Preparedness, Earthquake Preparedness and Response (SUPER) Project on BIPAD Portal',
                'donor_agency': 'UNDP',
                'start_date': date(2024, 5, 1),
                'end_date': date(2024, 7, 31),
                'contract_amount': Decimal('1004614'),
                'beneficiaries_count': 7056,
                'location': 'Bheri Municipality, Nalgad Municipality, Kushe Rural Municipality, Barekot Rural Municipality Jajarkot, Athbiskot Municipality and sanibheri Rural Municipality West Rukum',
                'core_activities': 'Bipad Portal data entry work Support to Earthquake affected Beneficiaries at Municipality/Rural Municipality',
                'status': 'completed'
            },
            {
                'title': 'Nepal livelihood and resilience Programme (NLRP) CYEP and IPM',
                'donor_agency': 'Australian aid, Caritas Australia, Caritas Nepal',
                'start_date': date(2018, 7, 1),
                'end_date': date(2023, 6, 30),
                'contract_amount': Decimal('13679539'),
                'beneficiaries_count': 1200,
                'location': 'Bheri Municipality ward 1,2,3,4,5 and Kushe Rural Municipality 5,6.7',
                'core_activities': 'Support priority households in diversifying income and livelihood portfolios, enhance community partners capacities, support women empowerment and rights targeting rural women.',
                'status': 'completed'
            },
            {
                'title': 'COVID-19 Response Nepal - 2021',
                'donor_agency': 'CARITAS/CRS Nepal',
                'start_date': date(2021, 6, 15),
                'end_date': date(2021, 9, 30),
                'contract_amount': Decimal('180000'),
                'beneficiaries_count': 4500,
                'location': 'Jajarkot District 7 Local',
                'core_activities': 'Coordinate with health stakeholders, hand over health equipment packages, provide COVID-19 protection information, home isolation kits and counseling.',
                'status': 'completed'
            },
            {
                'title': 'Child and Youth Empowerment Project',
                'donor_agency': 'Caritas Australia/Caritas Nepal, Banke Kohalpur',
                'start_date': date(2015, 7, 1),
                'end_date': date(2018, 6, 30),
                'contract_amount': Decimal('2883000'),
                'beneficiaries_count': 815,
                'location': 'Bheri Municipality (Khalanga) and Kushe Rural Municipality (Dhime) Jajarkot',
                'core_activities': 'Children and youth empowerment, child rights interaction, child club formation, human rights training, library establishment, income generating activities support.',
                'status': 'completed'
            }
        ]

        for project_data in projects_data:
            project, created = Project.objects.get_or_create(
                title=project_data['title'],
                defaults=project_data
            )
            if created:
                self.stdout.write(f'Project "{project_data["title"]}" created')

        # Create Board Members
        board_members_data = [
            {
                'name': 'Ammar Singh Rathaor',
                'position': 'President',
                'address': 'Bhery Municipality - 4 Jajarkot',
                'qualification': 'BA',
                'experience_years': 7,
                'expertise': 'Social Service',
                'order': 1
            },
            {
                'name': 'Dipak Shahi',
                'position': 'Vice President',
                'address': 'Naubasta-1 Banke',
                'qualification': 'B Ed',
                'experience_years': 8,
                'expertise': 'Woman Empowerment',
                'order': 2
            },
            {
                'name': 'Dhurba Raj Gautam',
                'position': 'Secretary',
                'address': 'Bheri Municipality - 1 Jajarkot',
                'qualification': 'BA',
                'experience_years': 8,
                'expertise': 'Good Governance, Peace Building',
                'order': 3
            },
            {
                'name': 'Laxman Nepali',
                'position': 'Vice Secretary',
                'address': 'Bheri Municipality - 1 Jajarkot',
                'qualification': 'SLC',
                'experience_years': 5,
                'expertise': 'Community Development Project, Councilor',
                'order': 4
            },
            {
                'name': 'Bindu Batala',
                'position': 'Treasurer',
                'address': 'Bheri Municipality 1 jajarkot',
                'qualification': 'BED',
                'experience_years': 8,
                'expertise': 'Teaching, Social Mobilization, Account management',
                'order': 5
            }
        ]

        for member_data in board_members_data:
            member, created = BoardMember.objects.get_or_create(
                name=member_data['name'],
                defaults=member_data
            )
            if created:
                self.stdout.write(f'Board member "{member_data["name"]}" created')

        # Create Key Staff Members
        staff_members_data = [
            {
                'name': 'Sahadev Basnet',
                'position': 'District Program Manager',
                'qualification': 'B. Ed.',
                'expertise': 'Overall program Management and execution, Evaluation, monitoring, and Coordination',
                'experience_duration': '20 years'
            },
            {
                'name': 'Babi Prakash Sharma',
                'position': 'Finance Officer',
                'qualification': 'MBS',
                'expertise': 'Handle day-to-day administrative and financial tasks, payment processing, bank reconciliations, monthly salaries, tax issues, financial reports.',
                'experience_duration': 'From December 2023-Ongoing'
            },
            {
                'name': 'Bhim Bahadur Sharki',
                'position': 'Data Management Officer',
                'qualification': 'B. Ed',
                'expertise': 'Database management of beneficiaries, Assist DDMC in database management.',
                'experience_duration': 'From December 2023-Ongoing'
            },
            {
                'name': 'Shreeja Acharya',
                'position': 'Documentation Officer',
                'qualification': 'Bsc. Agriculture',
                'expertise': 'Documentation of ongoing project advancements, monitoring and overseeing project activities, crafting case studies and generating comprehensive reports.',
                'experience_duration': 'From January 2024-Ongoing'
            },
            {
                'name': 'Dharma Raj Pariyar',
                'position': 'Social Mobilizer',
                'qualification': 'BED',
                'expertise': 'Managing beneficiary selection and data, distributing relief aid, educating communities on safe shelter practices, coordinating with stakeholders.',
                'experience_duration': 'From 2018- Ongoing'
            }
        ]

        for staff_data in staff_members_data:
            staff, created = StaffMember.objects.get_or_create(
                name=staff_data['name'],
                defaults=staff_data
            )
            if created:
                self.stdout.write(f'Staff member "{staff_data["name"]}" created')

        # Create Financial Records
        financial_data = [
            ('2062/2063', '432987.41'),
            ('2063/2064', '813385.74'),
            ('2064/2065', '1950256.76'),
            ('2065/2066', '1946559.26'),
            ('2066/2067', '3870698.26'),
            ('2067/2068', '6148764.76'),
            ('2068/2069', '3550000.00'),
            ('2069/2070', '7058377.00'),
            ('2070/2071', '2847531.97'),
            ('2071/2072', '2745637.60'),
            ('2072/2073', '2945209.00'),
            ('2073/2074', '2776783.00'),
            ('2074/075', '2422355.00'),
            ('2075/076', '2503536.00'),
            ('2076/077', '2057884.00'),
            ('2077/078', '2920644.00'),
            ('2078/2079', '3212577.00'),
            ('2079/2080', '2984898.00')
        ]

        for fiscal_year, turnover in financial_data:
            record, created = FinancialRecord.objects.get_or_create(
                fiscal_year=fiscal_year,
                defaults={'turnover': Decimal(turnover)}
            )
            if created:
                self.stdout.write(f'Financial record for {fiscal_year} created')

        # Create Objectives
        objectives_data = [
            'Creating employment opportunities for unemployed youths to make them self-reliant through various income generation and skill programs',
            'To support poor rural and urban households in diversifying their income and livelihood portfolios and improving their essential well-being.',
            'To empower participating communities and organizations to reduce disaster risks and make communities resilient.',
            'To address social concerns including child marriage, disability, gender violence, good governance, farmers rights, environment, health and nutrition through organized networks.',
            'Make health and education accessible to the disadvantaged and marginalized groups for their quality education and improve their health status.',
            'Supporting reducing poverty leads to the implementation of various income-generation activities for the core poor communities.',
            'Empowering and ensuring the rights of socially excluded groups, incredibly differently able people, Dalit, ethnic, women, and children through social mobilization',
            'Protect and promote the Child Rights Child Protection and Human rights in the community.'
        ]

        for i, obj_desc in enumerate(objectives_data, 1):
            objective, created = Objective.objects.get_or_create(
                description=obj_desc,
                defaults={'order': i}
            )
            if created:
                self.stdout.write(f'Objective {i} created')

        # Create Operational Approaches
        approaches_data = [
            ('Social Mobilization', 'Community-based approach to development'),
            ('Right Based Approach', 'Focus on human rights and dignity'),
            ('Gender and Social Inclusion', 'Ensuring inclusive participation'),
            ('Accountable Partnership and Consortium', 'Transparent partnerships'),
            ('Institutional and Human Resource Development', 'Capacity building focus'),
            ('Evidence-Based Advocacy', 'Data-driven advocacy efforts'),
            ('Demand Driven', 'Community-led development'),
            ('Volunteerism', 'Community volunteer engagement'),
            ('Child Rights, Child Protection, and Youth Empowerment', 'Focus on vulnerable populations')
        ]

        for i, (title, desc) in enumerate(approaches_data, 1):
            approach, created = Approach.objects.get_or_create(
                title=title,
                defaults={'description': desc, 'order': i}
            )
            if created:
                self.stdout.write(f'Approach "{title}" created')

        # Create Membership Data
        membership_data = [
            {
                'membership_type': 'founder',
                'male_count': 6,
                'female_count': 5,
                'dalit_count': 3,
                'janajati_count': 1,
                'others_count': 7
            },
            {
                'membership_type': 'general',
                'male_count': 34,
                'female_count': 23,
                'dalit_count': 20,
                'janajati_count': 11,
                'others_count': 26
            }
        ]

        for membership in membership_data:
            member, created = Membership.objects.get_or_create(
                membership_type=membership['membership_type'],
                defaults=membership
            )
            if created:
                self.stdout.write(f'Membership "{membership["membership_type"]}" created')

        # Create Publications
        publications_data = [
            'Strategic Plan',
            'Human Resource Management and Development Guideline',
            'Financial Management and Development Guideline',
            'Annual Reports',
            'Four Monthly Bulletin',
            'Calendar',
            'Booklet "Pyaja Kheti"(Onion)',
            'Social Accountability-Related Pamphlet',
            'Organizational Brochure'
        ]

        for pub_title in publications_data:
            publication, created = Publication.objects.get_or_create(
                title=pub_title,
                defaults={'description': f'Official publication: {pub_title}'}
            )
            if created:
                self.stdout.write(f'Publication "{pub_title}" created')

        # Create Policies
        policies_data = [
            'Child Protection Policy',
            'Gender Equity And Social Inclusion Policy',
            'Safeguarding Policy',
            'Human Resource Management and Development Policy',
            'Financial Resource Mobilization and Management Policy',
            'YARCN Strategic plan',
            'Library Management Policy'
        ]

        for policy_title in policies_data:
            policy, created = Policy.objects.get_or_create(
                title=policy_title,
                defaults={'description': f'Official policy: {policy_title}'}
            )
            if created:
                self.stdout.write(f'Policy "{policy_title}" created')

        # Create Bank Accounts
        bank_accounts_data = [
            {
                'account_type': 'Organizational Account',
                'bank_name': 'Nepal Bank Limited, Centre Office, Jajarkot, Nepal',
                'account_number': '07500101188495000001'
            },
            {
                'account_type': 'Project Account',
                'bank_name': 'Nepal Bank Limited, Jajarkot',
                'account_number': '075001101188599000001'
            }
        ]

        for account_data in bank_accounts_data:
            account, created = BankAccount.objects.get_or_create(
                account_type=account_data['account_type'],
                defaults=account_data
            )
            if created:
                self.stdout.write(f'Bank account "{account_data["account_type"]}" created')

        # Create Contact Persons
        contact_persons_data = [
            {
                'name': 'Ammar Singh Rathor',
                'position': 'Responsible Person',
                'address': 'Bheri Municipality- 4 Jajarkot',
                'phone': '089-430167',
                'email': '<EMAIL>'
            },
            {
                'name': 'Sahadev Basnet',
                'position': 'District Program Manager',
                'address': 'Bheri Municipality 1 Jajarkot',
                'phone': '**********',
                'email': '<EMAIL>'
            }
        ]

        for contact_data in contact_persons_data:
            contact, created = ContactPerson.objects.get_or_create(
                name=contact_data['name'],
                defaults=contact_data
            )
            if created:
                self.stdout.write(f'Contact person "{contact_data["name"]}" created')

        # Create Key Achievements
        achievements_data = [
            {
                'title': '100 Temporary Shelters Constructed',
                'description': 'Collaborating with Caritas Nepal to construct temporary shelters in earthquake-affected regions of Jajarkot and Rukum West.',
                'year': 2024,
                'icon': 'home'
            },
            {
                'title': '7 Temporary Learning Centers Established',
                'description': 'Established temporary learning centers to ensure continuation of education in earthquake-affected areas.',
                'year': 2024,
                'icon': 'school'
            },
            {
                'title': '3 Water Scheme Programs Implemented',
                'description': 'Successfully implemented water scheme programs to install pipes and construct taps in areas facing water accessibility challenges.',
                'year': 2023,
                'icon': 'tint'
            },
            {
                'title': 'Enhanced Food Security',
                'description': 'Improved food security through community-based farming and improved commercial seed production.',
                'year': 2022,
                'icon': 'seedling'
            },
            {
                'title': 'Diversified Livelihood Options',
                'description': 'Created diversified livelihood options through on-farm and off-farm interventions supporting small farmers.',
                'year': 2021,
                'icon': 'handshake'
            },
            {
                'title': 'Community Infrastructure Development',
                'description': 'Increased community productive base and accessibility to infrastructure including drinking water, schools, and health posts.',
                'year': 2020,
                'icon': 'building'
            }
        ]

        for achievement_data in achievements_data:
            achievement, created = Achievement.objects.get_or_create(
                title=achievement_data['title'],
                defaults=achievement_data
            )
            if created:
                self.stdout.write(f'Achievement "{achievement_data["title"]}" created')

        self.stdout.write(self.style.SUCCESS('Data seeding completed successfully!'))
        self.stdout.write(self.style.SUCCESS('You can now access the website and admin panel to see the data.'))