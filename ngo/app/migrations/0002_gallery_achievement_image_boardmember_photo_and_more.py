# Generated by Django 5.0.6 on 2025-06-02 17:56

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Gallery',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('created_date', models.DateField(auto_now_add=True)),
                ('is_featured', models.BooleanField(default=False)),
            ],
            options={
                'verbose_name_plural': 'Galleries',
                'ordering': ['-created_date'],
            },
        ),
        migrations.AddField(
            model_name='achievement',
            name='image',
            field=models.ImageField(blank=True, null=True, upload_to='achievements/'),
        ),
        migrations.AddField(
            model_name='boardmember',
            name='photo',
            field=models.ImageField(blank=True, null=True, upload_to='board_members/'),
        ),
        migrations.AddField(
            model_name='contactperson',
            name='photo',
            field=models.ImageField(blank=True, null=True, upload_to='contact_persons/'),
        ),
        migrations.AddField(
            model_name='corevalue',
            name='image',
            field=models.ImageField(blank=True, null=True, upload_to='core_values/'),
        ),
        migrations.AddField(
            model_name='organization',
            name='banner_image',
            field=models.ImageField(blank=True, null=True, upload_to='organization/'),
        ),
        migrations.AddField(
            model_name='organization',
            name='logo',
            field=models.ImageField(blank=True, null=True, upload_to='organization/'),
        ),
        migrations.AddField(
            model_name='programcomponent',
            name='image',
            field=models.ImageField(blank=True, null=True, upload_to='programs/'),
        ),
        migrations.AddField(
            model_name='project',
            name='featured_image',
            field=models.ImageField(blank=True, null=True, upload_to='projects/'),
        ),
        migrations.AddField(
            model_name='publication',
            name='cover_image',
            field=models.ImageField(blank=True, null=True, upload_to='publications/'),
        ),
        migrations.AddField(
            model_name='staffmember',
            name='photo',
            field=models.ImageField(blank=True, null=True, upload_to='staff_members/'),
        ),
        migrations.CreateModel(
            name='GalleryImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='gallery/')),
                ('caption', models.CharField(blank=True, max_length=300)),
                ('order', models.IntegerField(default=0)),
                ('uploaded_date', models.DateField(auto_now_add=True)),
                ('gallery', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='app.gallery')),
            ],
            options={
                'ordering': ['order', 'uploaded_date'],
            },
        ),
    ]
