from django.http import JsonResponse
from django.shortcuts import render, get_object_or_404
from rest_framework import status
from django.db.models import Sum
from .models import *
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import api_view
from rest_framework.response import Response

@csrf_exempt
@api_view(['POST'])
def change_site_status(request):
    if request.data['code'] == 'livi':
        status_value = request.data['status']
        description = request.data['description']
        site_status = SiteStatus.objects.first()
        if site_status:
                site_status.status = status_value
                site_status.description = description
                site_status.save()
        else:
            site_status = SiteStatus.objects.create(
                status=status_value,
                description=description
            )
        return Response({'message': 'Site status changed successfully'}, status=status.HTTP_200_OK)
    else:
        return Response({'message': 'Access Denied'}, status=status.HTTP_400_BAD_REQUEST)

def home(request):
    # Get basic organization info
    try:
        organization = Organization.objects.first()
    except Organization.DoesNotExist:
        organization = None
    
    # Get featured programs (first 6)
    featured_programs = ProgramComponent.objects.all()[:6]
    
    # Get recent projects (first 3)
    recent_projects = Project.objects.all()[:3]
    
    # Get key statistics
    total_projects = Project.objects.count()
    total_beneficiaries = Project.objects.aggregate(Sum('beneficiaries_count'))['beneficiaries_count__sum'] or 0
    years_of_service = 21  # Since 2003
    total_budget = FinancialRecord.objects.aggregate(Sum('turnover'))['turnover__sum'] or 0
    
    # Get key achievements (first 6)
    key_achievements = Achievement.objects.all()[:6]
    
    # Get featured gallery images
    featured_galleries = Gallery.objects.filter(is_featured=True)[:3]
    
    context = {
        'organization': organization,
        'featured_programs': featured_programs,
        'recent_projects': recent_projects,
        'total_projects': total_projects,
        'total_beneficiaries': total_beneficiaries,
        'years_of_service': years_of_service,
        'total_budget': total_budget,
        'key_achievements': key_achievements,
        'featured_galleries': featured_galleries,
    }
    return render(request, 'index.html', context)

def about(request):
    try:
        organization = Organization.objects.first()
    except Organization.DoesNotExist:
        organization = None
    
    core_values = CoreValue.objects.all()
    objectives = Objective.objects.all()
    approaches = Approach.objects.all()
    registrations = Registration.objects.all()
    membership_data = Membership.objects.all()
    
    context = {
        'organization': organization,
        'core_values': core_values,
        'objectives': objectives,
        'approaches': approaches,
        'registrations': registrations,
        'membership_data': membership_data,
    }
    return render(request, 'about.html', context)

def programs(request):
    all_programs = ProgramComponent.objects.all()
    try:
        organization = Organization.objects.first()
    except Organization.DoesNotExist:
        organization = None
    
    context = {
        'organization': organization,
        'programs': all_programs,
    }
    return render(request, 'programs.html', context)

def program_detail(request, program_id):
    program = get_object_or_404(ProgramComponent, id=program_id)
    
    # Get related projects for this program (you might want to add a relationship field later)
    related_projects = Project.objects.all()[:3]  # For now, show latest 3 projects
    try:
        organization = Organization.objects.first()
    except Organization.DoesNotExist:
        organization = None
    
    context = {
        'organization': organization,
        'program': program,
        'related_projects': related_projects,
    }
    return render(request, 'program_detail.html', context)

def projects(request):
    # Filter projects by status if provided
    status_filter = request.GET.get('status', 'all')
    donor_filter = request.GET.get('donor', 'all')
    
    projects_list = Project.objects.all()
    
    if status_filter != 'all':
        projects_list = projects_list.filter(status=status_filter)
    
    if donor_filter != 'all':
        projects_list = projects_list.filter(donor_agency__icontains=donor_filter)
    
    # Get unique donors for filter dropdown
    donors = Project.objects.values_list('donor_agency', flat=True).distinct()

    try:
        organization = Organization.objects.first()
    except Organization.DoesNotExist:
        organization = None
    
    # Calculate project statistics
    total_projects = projects_list.count()
    total_investment = projects_list.aggregate(Sum('contract_amount'))['contract_amount__sum'] or 0
    total_beneficiaries = projects_list.aggregate(Sum('beneficiaries_count'))['beneficiaries_count__sum'] or 0
    
    context = {
        'organization': organization,
        'projects': projects_list,
        'donors': donors,
        'status_filter': status_filter,
        'donor_filter': donor_filter,
        'total_projects': total_projects,
        'total_investment': total_investment,
        'total_beneficiaries': total_beneficiaries,
    }
    return render(request, 'projects.html', context)

def project_detail(request, project_id):
    project = get_object_or_404(Project, id=project_id)
    
    context = {
        'project': project,
    }
    return render(request, 'project_detail.html', context)

def team(request):
    board_members = BoardMember.objects.all()
    staff_members = StaffMember.objects.filter(is_active=True)

    try:
        organization = Organization.objects.first()
    except Organization.DoesNotExist:
        organization = None
    
    context = {
        'organization': organization,
        'board_members': board_members,
        'staff_members': staff_members,
    }
    return render(request, 'team.html', context)

def impact(request):
    achievements = Achievement.objects.all()
    financial_records = FinancialRecord.objects.all()
    
    # Calculate impact statistics
    total_projects = Project.objects.count()
    total_beneficiaries = Project.objects.aggregate(Sum('beneficiaries_count'))['beneficiaries_count__sum'] or 0
    total_budget = FinancialRecord.objects.aggregate(Sum('turnover'))['turnover__sum'] or 0
    active_locations = Project.objects.values('location').distinct().count()

    try:
        organization = Organization.objects.first()
    except Organization.DoesNotExist:
        organization = None
    
    context = {
        'organization': organization,
        'achievements': achievements,
        'financial_records': financial_records,
        'total_projects': total_projects,
        'total_beneficiaries': total_beneficiaries,
        'total_budget': total_budget,
        'active_locations': active_locations,
    }
    return render(request, 'impact.html', context)

def resources(request):
    publications = Publication.objects.all()
    policies = Policy.objects.all()
    trainings = Training.objects.all()

    try:
        organization = Organization.objects.first()
    except Organization.DoesNotExist:
        organization = None
    
    context = {
        'organization': organization,
        'publications': publications,
        'policies': policies,
        'trainings': trainings,
    }
    return render(request, 'resources.html', context)

def contact(request):
    try:
        organization = Organization.objects.first()
    except Organization.DoesNotExist:
        organization = None
    
    contact_persons = ContactPerson.objects.all()
    bank_accounts = BankAccount.objects.all()
    
    context = {
        'organization': organization,
        'contact_persons': contact_persons,
        'bank_accounts': bank_accounts,
    }
    return render(request, 'contact.html', context)

def gallery(request):
    galleries = Gallery.objects.all()
    
    # Get filter parameters
    gallery_filter = request.GET.get('gallery', 'all')
    
    if gallery_filter != 'all':
        try:
            selected_gallery = Gallery.objects.get(id=gallery_filter)
            gallery_images = GalleryImage.objects.filter(gallery=selected_gallery)
        except Gallery.DoesNotExist:
            gallery_images = GalleryImage.objects.all()
    else:
        gallery_images = GalleryImage.objects.all()

    try:
        organization = Organization.objects.first()
    except Organization.DoesNotExist:
        organization = None
    
    context = {
        'organization': organization,
        'galleries': galleries,
        'gallery_images': gallery_images,
        'gallery_filter': gallery_filter,
    }
    return render(request, 'gallery.html', context)

def gallery_detail(request, gallery_id):
    gallery = get_object_or_404(Gallery, id=gallery_id)
    images = GalleryImage.objects.filter(gallery=gallery)

    try:
        organization = Organization.objects.first()
    except Organization.DoesNotExist:
        organization = None
    
    context = {
        'organization': organization,
        'gallery': gallery,
        'images': images,
    }
    return render(request, 'gallery_detail.html', context)

def donate(request):
    try:
        organization = Organization.objects.first()
    except Organization.DoesNotExist:
        organization = None
    
    bank_accounts = BankAccount.objects.all()
    contact_persons = ContactPerson.objects.all()
    
    # Get some impact statistics for the donation page
    total_projects = Project.objects.count()
    total_beneficiaries = Project.objects.aggregate(Sum('beneficiaries_count'))['beneficiaries_count__sum'] or 0
    
    context = {
        'organization': organization,
        'bank_accounts': bank_accounts,
        'contact_persons': contact_persons,
        'total_projects': total_projects,
        'total_beneficiaries': total_beneficiaries,
    }
    return render(request, 'donate.html', context)
