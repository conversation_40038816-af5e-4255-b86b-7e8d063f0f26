from django.db import models
from django.utils import timezone

class SiteStatus(models.Model):
    status = models.BooleanField(default=True)
    description = models.TextField()
    created_date = models.DateField(auto_now_add=True)
    updated_date = models.DateField(auto_now=True)
    
    def __str__(self):
        return self.status

class Organization(models.Model):
    name = models.CharField(max_length=200, default="Youth Awareness Raising Center Nepal (YARCN)")
    tagline = models.CharField(max_length=300, default="Empowering youth for socio-economic transformation")
    address = models.TextField()
    phone = models.CharField(max_length=50)
    email = models.EmailField()
    website = models.URLField(blank=True)
    established_date = models.DateField()
    vision = models.TextField()
    mission = models.TextField()
    goal = models.TextField()
    logo = models.ImageField(upload_to='organization/', blank=True, null=True)
    banner_image = models.ImageField(upload_to='organization/', blank=True, null=True)
    profile_document = models.FileField(upload_to='organization/documents/', blank=True, null=True)
    
    def __str__(self):
        return self.name

class CoreValue(models.Model):
    title = models.CharField(max_length=100)
    description = models.TextField()
    icon = models.CharField(max_length=50, blank=True)  # For CSS icon classes
    
    def __str__(self):
        return self.title

class Registration(models.Model):
    office_name = models.CharField(max_length=200)
    registration_date = models.DateField()
    registration_number = models.CharField(max_length=100)
    renewal_date = models.DateField(null=True, blank=True)
    
    def __str__(self):
        return f"{self.office_name} - {self.registration_number}"

class ProgramComponent(models.Model):
    title = models.CharField(max_length=200)
    description = models.TextField()
    icon = models.CharField(max_length=50, blank=True)
    image = models.ImageField(upload_to='programs/', blank=True, null=True)
    program_document = models.FileField(upload_to='programs/documents/', blank=True, null=True)
    order = models.IntegerField(default=0)
    
    class Meta:
        ordering = ['order']
    
    def __str__(self):
        return self.title

class Project(models.Model):
    title = models.CharField(max_length=300)
    donor_agency = models.CharField(max_length=200)
    start_date = models.DateField()
    end_date = models.DateField()
    contract_amount = models.DecimalField(max_digits=15, decimal_places=2)
    beneficiaries_count = models.IntegerField()
    location = models.TextField()
    core_activities = models.TextField()
    featured_image = models.ImageField(upload_to='projects/', blank=True, null=True)
    project_document = models.FileField(upload_to='projects/documents/', blank=True, null=True)
    status = models.CharField(max_length=20, choices=[
        ('completed', 'Completed'),
        ('ongoing', 'Ongoing'),
        ('planned', 'Planned')
    ], default='completed')
    
    class Meta:
        ordering = ['-start_date']
    
    def __str__(self):
        return self.title

class BoardMember(models.Model):
    name = models.CharField(max_length=100)
    position = models.CharField(max_length=100)
    address = models.CharField(max_length=200)
    qualification = models.CharField(max_length=100)
    experience_years = models.IntegerField()
    expertise = models.TextField()
    photo = models.ImageField(upload_to='board_members/', blank=True, null=True)
    order = models.IntegerField(default=0)
    
    class Meta:
        ordering = ['order']
    
    def __str__(self):
        return f"{self.name} - {self.position}"

class StaffMember(models.Model):
    name = models.CharField(max_length=100)
    position = models.CharField(max_length=100)
    qualification = models.CharField(max_length=100)
    expertise = models.TextField()
    experience_duration = models.CharField(max_length=100)
    photo = models.ImageField(upload_to='staff_members/', blank=True, null=True)
    is_active = models.BooleanField(default=True)
    
    def __str__(self):
        return f"{self.name} - {self.position}"

class Achievement(models.Model):
    title = models.CharField(max_length=300)
    description = models.TextField()
    year = models.IntegerField(null=True, blank=True)
    icon = models.CharField(max_length=50, blank=True)
    
    def __str__(self):
        return self.title

class FinancialRecord(models.Model):
    fiscal_year = models.CharField(max_length=20)
    turnover = models.DecimalField(max_digits=15, decimal_places=2)
    
    class Meta:
        ordering = ['fiscal_year']
    
    def __str__(self):
        return f"{self.fiscal_year} - Rs. {self.turnover}"

class Publication(models.Model):
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    publication_date = models.DateField(null=True, blank=True)
    publication_file = models.FileField(upload_to='publications/documents/', blank=True, null=True)
    file_url = models.URLField(blank=True)
    cover_image = models.ImageField(upload_to='publications/', blank=True, null=True)
    
    def __str__(self):
        return self.title

class Policy(models.Model):
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    effective_date = models.DateField(null=True, blank=True)
    file_url = models.URLField(blank=True)
    
    def __str__(self):
        return self.title

class Training(models.Model):
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    duration = models.CharField(max_length=100)
    date = models.DateField(null=True, blank=True)
    
    def __str__(self):
        return self.title

class Objective(models.Model):
    description = models.TextField()
    order = models.IntegerField(default=0)
    
    class Meta:
        ordering = ['order']
    
    def __str__(self):
        return self.description[:100] + "..." if len(self.description) > 100 else self.description

class Approach(models.Model):
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    order = models.IntegerField(default=0)
    
    class Meta:
        ordering = ['order']
    
    def __str__(self):
        return self.title

class Membership(models.Model):
    type_choices = [
        ('founder', 'Founder Membership'),
        ('general', 'General Membership')
    ]
    membership_type = models.CharField(max_length=20, choices=type_choices)
    male_count = models.IntegerField(default=0)
    female_count = models.IntegerField(default=0)
    dalit_count = models.IntegerField(default=0)
    janajati_count = models.IntegerField(default=0)
    others_count = models.IntegerField(default=0)
    
    @property
    def total_count(self):
        return self.male_count + self.female_count
    
    def __str__(self):
        return f"{self.get_membership_type_display()} - {self.total_count} members"

class BankAccount(models.Model):
    account_type = models.CharField(max_length=100)
    bank_name = models.CharField(max_length=200)
    account_number = models.CharField(max_length=50)
    
    def __str__(self):
        return f"{self.account_type} - {self.bank_name}"

class ContactPerson(models.Model):
    name = models.CharField(max_length=100)
    position = models.CharField(max_length=100)
    address = models.CharField(max_length=200)
    phone = models.CharField(max_length=50)
    email = models.EmailField()
    photo = models.ImageField(upload_to='contact_persons/', blank=True, null=True)
    
    def __str__(self):
        return f"{self.name} - {self.position}"

# Gallery Models
class Gallery(models.Model):
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    created_date = models.DateField(auto_now_add=True)
    is_featured = models.BooleanField(default=False)
    
    class Meta:
        ordering = ['-created_date']
        verbose_name_plural = "Galleries"
    
    def __str__(self):
        return self.title

class GalleryImage(models.Model):
    gallery = models.ForeignKey(Gallery, related_name='images', on_delete=models.CASCADE)
    image = models.ImageField(upload_to='gallery/')
    caption = models.CharField(max_length=300, blank=True)
    order = models.IntegerField(default=0)
    uploaded_date = models.DateField(auto_now_add=True)
    
    class Meta:
        ordering = ['order', 'uploaded_date']
    
    def __str__(self):
        return f"{self.gallery.title} - Image {self.order}"
