from django.contrib import admin
from .models import *

@admin.register(Organization)
class OrganizationAdmin(admin.ModelAdmin):
    list_display = ['name', 'phone', 'email', 'established_date']
    search_fields = ['name', 'email']
    fields = ['name', 'tagline', 'address', 'phone', 'email', 'website', 'established_date', 
              'vision', 'mission', 'goal', 'logo', 'banner_image', 'profile_document']

@admin.register(CoreValue)
class CoreValueAdmin(admin.ModelAdmin):
    list_display = ['title', 'icon']
    search_fields = ['title']
    fields = ['title', 'description', 'icon']

@admin.register(Registration)
class RegistrationAdmin(admin.ModelAdmin):
    list_display = ['office_name', 'registration_number', 'registration_date']
    list_filter = ['registration_date']
    search_fields = ['office_name', 'registration_number']

@admin.register(ProgramComponent)
class ProgramComponentAdmin(admin.ModelAdmin):
    list_display = ['title', 'order']
    list_editable = ['order']
    ordering = ['order']
    fields = ['title', 'description', 'icon', 'image', 'program_document', 'order']

@admin.register(Project)
class ProjectAdmin(admin.ModelAdmin):
    list_display = ['title', 'donor_agency', 'start_date', 'end_date', 'status', 'contract_amount']
    list_filter = ['status', 'start_date', 'donor_agency']
    search_fields = ['title', 'donor_agency', 'location']
    date_hierarchy = 'start_date'
    fields = ['title', 'donor_agency', 'start_date', 'end_date', 'contract_amount', 
              'beneficiaries_count', 'location', 'core_activities', 'featured_image', 
              'project_document', 'status']

@admin.register(BoardMember)
class BoardMemberAdmin(admin.ModelAdmin):
    list_display = ['name', 'position', 'qualification', 'experience_years', 'order']
    list_editable = ['order']
    search_fields = ['name', 'position']
    ordering = ['order']

@admin.register(StaffMember)
class StaffMemberAdmin(admin.ModelAdmin):
    list_display = ['name', 'position', 'qualification', 'is_active']
    list_filter = ['is_active', 'position']
    search_fields = ['name', 'position']

@admin.register(Achievement)
class AchievementAdmin(admin.ModelAdmin):
    list_display = ['title', 'year', 'icon']
    list_filter = ['year']
    search_fields = ['title']
    fields = ['title', 'description', 'year', 'icon']

@admin.register(FinancialRecord)
class FinancialRecordAdmin(admin.ModelAdmin):
    list_display = ['fiscal_year', 'turnover']
    ordering = ['fiscal_year']

@admin.register(Publication)
class PublicationAdmin(admin.ModelAdmin):
    list_display = ['title', 'publication_date']
    list_filter = ['publication_date']
    search_fields = ['title']
    fields = ['title', 'description', 'publication_date', 'cover_image', 
              'publication_file', 'file_url']

@admin.register(Policy)
class PolicyAdmin(admin.ModelAdmin):
    list_display = ['title', 'effective_date']
    list_filter = ['effective_date']
    search_fields = ['title']

@admin.register(Training)
class TrainingAdmin(admin.ModelAdmin):
    list_display = ['title', 'duration', 'date']
    list_filter = ['date']
    search_fields = ['title']

@admin.register(Objective)
class ObjectiveAdmin(admin.ModelAdmin):
    list_display = ['__str__', 'order']
    list_editable = ['order']
    ordering = ['order']

@admin.register(Approach)
class ApproachAdmin(admin.ModelAdmin):
    list_display = ['title', 'order']
    list_editable = ['order']
    ordering = ['order']

@admin.register(Membership)
class MembershipAdmin(admin.ModelAdmin):
    list_display = ['membership_type', 'total_count', 'male_count', 'female_count']

@admin.register(BankAccount)
class BankAccountAdmin(admin.ModelAdmin):
    list_display = ['account_type', 'bank_name', 'account_number']

@admin.register(ContactPerson)
class ContactPersonAdmin(admin.ModelAdmin):
    list_display = ['name', 'position', 'phone', 'email']
    search_fields = ['name', 'position']

class GalleryImageInline(admin.TabularInline):
    model = GalleryImage
    extra = 1
    fields = ['image', 'caption', 'order']
    ordering = ['order']

@admin.register(Gallery)
class GalleryAdmin(admin.ModelAdmin):
    list_display = ['title', 'created_date', 'is_featured']
    list_filter = ['is_featured', 'created_date']
    search_fields = ['title', 'description']
    inlines = [GalleryImageInline]
    date_hierarchy = 'created_date'

@admin.register(GalleryImage)
class GalleryImageAdmin(admin.ModelAdmin):
    list_display = ['gallery', 'caption', 'order', 'uploaded_date']
    list_filter = ['gallery', 'uploaded_date']
    search_fields = ['caption', 'gallery__title']
    ordering = ['gallery', 'order']
