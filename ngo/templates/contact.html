{% extends 'layouts/base.html' %}

{% block title %}Contact Us - {{ organization.name|default:"Youth Awareness Raising Center Nepal (YARCN)" }}{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="gradient-primary text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-bold mb-6">Contact Us</h1>
            <p class="text-xl text-blue-100 max-w-3xl mx-auto">
                Get in touch with us to learn more about our work or to collaborate
            </p>
        </div>
    </div>
</section>

<!-- Contact Information -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <!-- Contact Form -->
            <div>
                <h2 class="text-3xl font-bold text-gray-900 mb-6">Send us a Message</h2>
                <form class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="first_name" class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                            <input type="text" id="first_name" name="first_name" required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        <div>
                            <label for="last_name" class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                            <input type="text" id="last_name" name="last_name" required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                    </div>
                    
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                        <input type="email" id="email" name="email" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                        <input type="tel" id="phone" name="phone"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    
                    <div>
                        <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                        <select id="subject" name="subject" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">Select a subject</option>
                            <option value="general">General Inquiry</option>
                            <option value="partnership">Partnership Opportunity</option>
                            <option value="volunteer">Volunteer Application</option>
                            <option value="donation">Donation Inquiry</option>
                            <option value="media">Media & Press</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="message" class="block text-sm font-medium text-gray-700 mb-2">Message</label>
                        <textarea id="message" name="message" rows="5" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="Tell us how we can help you..."></textarea>
                    </div>
                    
                    <button type="submit" 
                        class="w-full gradient-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                        <i class="fas fa-paper-plane mr-2"></i>Send Message
                    </button>
                </form>
            </div>
            
            <!-- Contact Details -->
            <div class="space-y-8">
                <div>
                    <h2 class="text-3xl font-bold text-gray-900 mb-6">Get in Touch</h2>
                    <p class="text-lg text-gray-600 mb-8">
                        We'd love to hear from you. Here's how you can reach us or visit our office.
                    </p>
                </div>
                
                <!-- Organization Contact -->
                {% if organization %}
                <div class="bg-gray-50 p-6 rounded-xl">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Organization Contact</h3>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <div class="w-10 h-10 gradient-primary rounded-lg flex items-center justify-center mr-4">
                                <i class="fas fa-map-marker-alt text-white"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900">Address</p>
                                <p class="text-gray-600">{{ organization.address }}</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center">
                            <div class="w-10 h-10 gradient-primary rounded-lg flex items-center justify-center mr-4">
                                <i class="fas fa-phone text-white"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900">Phone</p>
                                <p class="text-gray-600">{{ organization.phone }}</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center">
                            <div class="w-10 h-10 gradient-primary rounded-lg flex items-center justify-center mr-4">
                                <i class="fas fa-envelope text-white"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900">Email</p>
                                <p class="text-gray-600">{{ organization.email }}</p>
                            </div>
                        </div>
                        
                        {% if organization.website %}
                        <div class="flex items-center">
                            <div class="w-10 h-10 gradient-primary rounded-lg flex items-center justify-center mr-4">
                                <i class="fas fa-globe text-white"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900">Website</p>
                                <p class="text-gray-600">{{ organization.website }}</p>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}
                
                <!-- Office Hours -->
                <div class="bg-gray-50 p-6 rounded-xl">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Office Hours</h3>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Sunday - Thursday</span>
                            <span class="text-gray-900 font-medium">10:00 AM - 5:00 PM</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Friday</span>
                            <span class="text-gray-900 font-medium">10:00 AM - 3:00 PM</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Saturday</span>
                            <span class="text-red-600 font-medium">Closed</span>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h3>
                    <div class="space-y-3">
                        <a href="{% url 'donate' %}" class="flex items-center p-3 bg-white rounded-lg hover:shadow-md transition-shadow">
                            <div class="w-8 h-8 gradient-primary rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-heart text-white text-sm"></i>
                            </div>
                            <span class="text-gray-700 font-medium">Make a Donation</span>
                        </a>
                        
                        <a href="{% url 'programs' %}" class="flex items-center p-3 bg-white rounded-lg hover:shadow-md transition-shadow">
                            <div class="w-8 h-8 gradient-secondary rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-hands-helping text-white text-sm"></i>
                            </div>
                            <span class="text-gray-700 font-medium">Volunteer With Us</span>
                        </a>
                        
                        <a href="{% url 'resources' %}" class="flex items-center p-3 bg-white rounded-lg hover:shadow-md transition-shadow">
                            <div class="w-8 h-8 gradient-accent rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-download text-white text-sm"></i>
                            </div>
                            <span class="text-gray-700 font-medium">Download Resources</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Persons -->
{% if contact_persons %}
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Key Contact Persons</h2>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                Reach out to our key personnel for specific inquiries
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            {% for contact in contact_persons %}
            <div class="bg-white p-6 rounded-xl shadow-lg card-hover">
                <div class="flex items-center mb-4">
                    <div class="w-16 h-16 gradient-primary rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-user text-white text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-gray-900">{{ contact.name }}</h3>
                        <p class="text-blue-600 font-medium">{{ contact.position }}</p>
                    </div>
                </div>
                
                <div class="space-y-2 text-sm text-gray-600">
                    <div class="flex items-center">
                        <i class="fas fa-map-marker-alt w-4 mr-3"></i>
                        <span>{{ contact.address }}</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-phone w-4 mr-3"></i>
                        <span>{{ contact.phone }}</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-envelope w-4 mr-3"></i>
                        <span>{{ contact.email }}</span>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

<!-- Bank Account Information -->
{% if bank_accounts %}
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Banking Information</h2>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                For donations and financial transactions, you can use our official bank accounts
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            {% for account in bank_accounts %}
            <div class="bg-gradient-to-br from-blue-50 to-indigo-50 p-8 rounded-xl border border-blue-200">
                <div class="flex items-center mb-6">
                    <div class="w-12 h-12 gradient-primary rounded-lg flex items-center justify-center mr-4">
                        <i class="fas fa-university text-white text-lg"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900">{{ account.account_type }}</h3>
                </div>
                
                <div class="space-y-3">
                    <div>
                        <p class="text-sm font-medium text-gray-700">Bank Name</p>
                        <p class="text-gray-900">{{ account.bank_name }}</p>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-700">Account Number</p>
                        <p class="text-gray-900 font-mono text-lg">{{ account.account_number }}</p>
                    </div>
                </div>
                
                <div class="mt-6 p-4 bg-white rounded-lg">
                    <div class="flex items-center text-sm text-gray-600">
                        <i class="fas fa-info-circle mr-2"></i>
                        <span>Please mention the purpose of donation when making transfers</span>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

<!-- Map Section -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Find Us</h2>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                Visit our office in Jajarkot, Nepal
            </p>
        </div>
        
        <div class="bg-white p-8 rounded-xl shadow-lg">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                <div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">Our Location</h3>
                    <p class="text-lg text-gray-600 mb-6">
                        We are located in the heart of Jajarkot district, making us easily accessible to the communities we serve.
                    </p>
                    
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <div class="w-10 h-10 gradient-primary rounded-lg flex items-center justify-center mr-4">
                                <i class="fas fa-map-pin text-white"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900">District Office</p>
                                <p class="text-gray-600">Bheri Municipality-4, Jajarkot</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center">
                            <div class="w-10 h-10 gradient-primary rounded-lg flex items-center justify-center mr-4">
                                <i class="fas fa-car text-white"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900">Transportation</p>
                                <p class="text-gray-600">Accessible by road from Nepalgunj and Surkhet</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center">
                            <div class="w-10 h-10 gradient-primary rounded-lg flex items-center justify-center mr-4">
                                <i class="fas fa-clock text-white"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900">Best Time to Visit</p>
                                <p class="text-gray-600">October to March (clear weather)</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-100 rounded-lg h-64 flex items-center justify-center">
                    <div class="text-center text-gray-500">
                        <i class="fas fa-map-marked-alt text-4xl mb-4"></i>
                        <p>Interactive Map</p>
                        <p class="text-sm">Google Maps integration would go here</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action -->
<section class="py-16 gradient-primary text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-bold mb-6">Ready to Make a Difference?</h2>
        <p class="text-xl mb-8 text-blue-100 max-w-3xl mx-auto">
            Join us in our mission to empower communities and create lasting change in Nepal
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{% url 'programs' %}" class="bg-white text-blue-600 px-8 py-3 rounded-full font-semibold hover:bg-blue-50 transition-colors">
                <i class="fas fa-hands-helping mr-2"></i>Explore Programs
            </a>
            <a href="{% url 'impact' %}" class="border-2 border-white text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                <i class="fas fa-chart-line mr-2"></i>See Our Impact
            </a>
        </div>
    </div>
</section>
{% endblock %} 