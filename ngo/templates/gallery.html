{% extends 'layouts/base.html' %}

{% block title %}Gallery - {{ organization.name|default:"Youth Awareness Raising Center Nepal (YARCN)" }}{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="gradient-primary text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-bold mb-6">Gallery</h1>
            <p class="text-xl text-blue-100 max-w-3xl mx-auto">
                Visual stories of our work and impact in communities across Nepal
            </p>
        </div>
    </div>
</section>

<!-- Gallery Filters -->
<section class="py-8 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-gray-50 p-6 rounded-xl">
            <form method="GET" class="flex flex-col md:flex-row gap-4 items-center">
                <div class="flex-1">
                    <label for="gallery" class="block text-sm font-medium text-gray-700 mb-2">Filter by Collection</label>
                    <select name="gallery" id="gallery" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="all" {% if gallery_filter == 'all' %}selected{% endif %}>All Collections</option>
                        {% for gallery in galleries %}
                        <option value="{{ gallery.id }}" {% if gallery_filter == gallery.id|stringformat:"s" %}selected{% endif %}>{{ gallery.title }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="flex items-end">
                    <button type="submit" class="gradient-primary text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                        <i class="fas fa-filter mr-2"></i>Filter
                    </button>
                </div>
            </form>
        </div>
    </div>
</section>

<!-- Gallery Grid -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {% if gallery_images %}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {% for image in gallery_images %}
            <div class="group relative overflow-hidden rounded-xl shadow-lg card-hover cursor-pointer" onclick="openLightbox('{{ image.image.url }}', '{{ image.caption|default:image.gallery.title }}')">
                <div class="aspect-square overflow-hidden">
                    <img src="{{ image.image.url }}" alt="{{ image.caption|default:image.gallery.title }}" class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                </div>
                
                <!-- Overlay -->
                <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300 flex items-center justify-center">
                    <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-center">
                        <i class="fas fa-search-plus text-white text-2xl mb-2"></i>
                        <p class="text-white text-sm font-medium">View Image</p>
                    </div>
                </div>
                
                <!-- Caption -->
                {% if image.caption %}
                <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4">
                    <p class="text-white text-sm">{{ image.caption }}</p>
                </div>
                {% endif %}
                
                <!-- Gallery Tag -->
                <div class="absolute top-3 left-3">
                    <span class="bg-white bg-opacity-90 text-gray-800 px-2 py-1 rounded-full text-xs font-medium">
                        {{ image.gallery.title }}
                    </span>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-16">
            <div class="w-24 h-24 gradient-primary rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-images text-white text-3xl"></i>
            </div>
            <h3 class="text-2xl font-semibold text-gray-900 mb-4">No Images Found</h3>
            <p class="text-gray-600 max-w-md mx-auto mb-8">
                {% if gallery_filter != 'all' %}
                No images found in the selected collection. Try viewing all collections.
                {% else %}
                Our gallery is being updated. Please check back soon for photos of our work and community activities.
                {% endif %}
            </p>
            <a href="{% url 'gallery' %}" class="gradient-primary text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                <i class="fas fa-refresh mr-2"></i>View All
            </a>
        </div>
        {% endif %}
    </div>
</section>

<!-- Gallery Collections -->
{% if galleries %}
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Collections</h2>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                Organized collections showcasing different aspects of our work
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {% for gallery in galleries %}
            <div class="bg-white rounded-xl shadow-lg overflow-hidden card-hover">
                {% if gallery.images.first %}
                <div class="h-48 overflow-hidden">
                    <img src="{{ gallery.images.first.image.url }}" alt="{{ gallery.title }}" class="w-full h-full object-cover">
                </div>
                {% else %}
                <div class="h-48 gradient-primary flex items-center justify-center">
                    <i class="fas fa-camera text-white text-4xl"></i>
                </div>
                {% endif %}
                
                <div class="p-6">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="text-xl font-semibold text-gray-900">{{ gallery.title }}</h3>
                        {% if gallery.is_featured %}
                        <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium">Featured</span>
                        {% endif %}
                    </div>
                    
                    <p class="text-gray-600 mb-4">{{ gallery.description|truncatewords:15 }}</p>
                    
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-500">
                            <i class="fas fa-images mr-1"></i>
                            <span>{{ gallery.images.count }} photos</span>
                        </div>
                        <a href="{% url 'gallery_detail' gallery.id %}" class="text-blue-600 hover:text-blue-800 font-medium">
                            View Collection <i class="fas fa-arrow-right ml-1"></i>
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

<!-- Stats Section -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Gallery Statistics</h2>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                Capturing moments that matter
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div class="text-center">
                <div class="w-16 h-16 gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-camera text-white text-2xl"></i>
                </div>
                <div class="text-3xl font-bold text-gray-900 mb-2">{{ gallery_images|length }}+</div>
                <div class="text-gray-600">Total Photos</div>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 gradient-secondary rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-folder text-white text-2xl"></i>
                </div>
                <div class="text-3xl font-bold text-gray-900 mb-2">{{ galleries|length }}</div>
                <div class="text-gray-600">Collections</div>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 gradient-accent rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-map-marker-alt text-white text-2xl"></i>
                </div>
                <div class="text-3xl font-bold text-gray-900 mb-2">25+</div>
                <div class="text-gray-600">Locations</div>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-clock text-white text-2xl"></i>
                </div>
                <div class="text-3xl font-bold text-gray-900 mb-2">21+</div>
                <div class="text-gray-600">Years Documented</div>
            </div>
        </div>
    </div>
</section>

<!-- Lightbox Modal -->
<div id="lightbox" class="fixed inset-0 bg-black bg-opacity-90 z-50 hidden flex items-center justify-center p-4">
    <div class="relative max-w-4xl max-h-full">
        <button onclick="closeLightbox()" class="absolute top-4 right-4 text-white text-2xl z-10 w-10 h-10 bg-black bg-opacity-50 rounded-full flex items-center justify-center hover:bg-opacity-75 transition-all">
            <i class="fas fa-times"></i>
        </button>
        
        <img id="lightbox-image" src="" alt="" class="max-w-full max-h-full object-contain">
        
        <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-6">
            <p id="lightbox-caption" class="text-white text-center"></p>
        </div>
    </div>
</div>

<!-- Call to Action -->
<section class="py-16 gradient-primary text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-bold mb-6">Share Your Story</h2>
        <p class="text-xl mb-8 text-blue-100 max-w-3xl mx-auto">
            Have photos from our projects or events? We'd love to feature them in our gallery
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{% url 'contact' %}" class="bg-white text-blue-600 px-8 py-3 rounded-full font-semibold hover:bg-blue-50 transition-colors">
                <i class="fas fa-camera mr-2"></i>Submit Photos
            </a>
            <a href="{% url 'projects' %}" class="border-2 border-white text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                <i class="fas fa-eye mr-2"></i>View Our Work
            </a>
        </div>
    </div>
</section>

<!-- JavaScript for Lightbox -->
<script>
function openLightbox(imageSrc, caption) {
    const lightbox = document.getElementById('lightbox');
    const lightboxImage = document.getElementById('lightbox-image');
    const lightboxCaption = document.getElementById('lightbox-caption');
    
    lightboxImage.src = imageSrc;
    lightboxCaption.textContent = caption;
    lightbox.classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function closeLightbox() {
    const lightbox = document.getElementById('lightbox');
    lightbox.classList.add('hidden');
    document.body.style.overflow = 'auto';
}

// Close lightbox on Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeLightbox();
    }
});

// Close lightbox when clicking outside the image
document.getElementById('lightbox').addEventListener('click', function(e) {
    if (e.target === this) {
        closeLightbox();
    }
});
</script>
{% endblock %} 