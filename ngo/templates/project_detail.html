{% extends 'layouts/base.html' %}

{% block title %}{{ project.title }} - Projects - {{ organization.name|default:"Youth Awareness Raising Center Nepal (YARCN)" }}{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="relative bg-gray-900 text-white py-20">
    {% if project.featured_image %}
    <div class="absolute inset-0">
        <img src="{{ project.featured_image.url }}" alt="{{ project.title }}" class="w-full h-full object-cover opacity-50">
    </div>
    {% else %}
    <div class="absolute inset-0 gradient-primary opacity-80"></div>
    {% endif %}
    
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="max-w-4xl">
            <div class="flex items-center mb-4">
                <span class="px-3 py-1 bg-{% if project.status == 'ongoing' %}green{% elif project.status == 'completed' %}blue{% else %}yellow{% endif %}-600 text-white text-sm font-semibold rounded-full uppercase tracking-wide">
                    {{ project.status }}
                </span>
                <span class="ml-4 text-lg text-gray-300">{{ project.start_date.year }} - {{ project.end_date.year }}</span>
            </div>
            <h1 class="text-4xl md:text-5xl font-bold mb-6">{{ project.title }}</h1>
            <p class="text-xl text-gray-200 mb-8">
                Funded by {{ project.donor_agency }}
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <i class="fas fa-users text-2xl mr-3"></i>
                        <div>
                            <div class="text-2xl font-bold">{{ project.beneficiaries_count|floatformat:0 }}</div>
                            <div class="text-sm text-gray-300">Beneficiaries</div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <i class="fas fa-coins text-2xl mr-3"></i>
                        <div>
                            <div class="text-2xl font-bold">₹{{ project.contract_amount|floatformat:0 }}</div>
                            <div class="text-sm text-gray-300">Budget</div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <i class="fas fa-map-marker-alt text-2xl mr-3"></i>
                        <div>
                            <div class="text-lg font-bold">{{ project.location|truncatewords:3 }}</div>
                            <div class="text-sm text-gray-300">Location</div>
                        </div>
                    </div>
                </div>
            </div>
            
            {% if project.project_document %}
            <div class="mt-8">
                <a href="{{ project.project_document.url }}" target="_blank" class="inline-flex items-center bg-white bg-opacity-20 backdrop-blur-sm text-white px-6 py-3 rounded-lg font-semibold hover:bg-opacity-30 transition-all duration-300">
                    <i class="fas fa-download mr-2"></i>Download Project Document
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</section>

<!-- Project Details -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <div class="prose max-w-none">
                    <h2 class="text-3xl font-bold text-gray-900 mb-6">Project Overview</h2>
                    <div class="text-lg text-gray-600 leading-relaxed mb-8">
                        {{ project.core_activities|linebreaks }}
                    </div>
                    
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">Project Location</h3>
                    <p class="text-gray-600 mb-8">{{ project.location }}</p>
                    
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">Duration</h3>
                    <div class="bg-gray-50 p-6 rounded-xl">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-sm text-gray-500">Start Date</div>
                                <div class="text-lg font-semibold text-gray-900">{{ project.start_date|date:"F d, Y" }}</div>
                            </div>
                            <div class="w-12 h-0.5 bg-gray-300"></div>
                            <div>
                                <div class="text-sm text-gray-500">End Date</div>
                                <div class="text-lg font-semibold text-gray-900">{{ project.end_date|date:"F d, Y" }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="space-y-8">
                <!-- Project Info Card -->
                <div class="bg-gray-50 p-6 rounded-xl">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Project Information</h3>
                    <div class="space-y-4">
                        <div>
                            <div class="text-sm font-medium text-gray-700">Donor Agency</div>
                            <div class="text-gray-900">{{ project.donor_agency }}</div>
                        </div>
                        
                        <div>
                            <div class="text-sm font-medium text-gray-700">Status</div>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-{% if project.status == 'ongoing' %}green{% elif project.status == 'completed' %}blue{% else %}yellow{% endif %}-100 text-{% if project.status == 'ongoing' %}green{% elif project.status == 'completed' %}blue{% else %}yellow{% endif %}-800">
                                {{ project.get_status_display }}
                            </span>
                        </div>
                        
                        <div>
                            <div class="text-sm font-medium text-gray-700">Total Beneficiaries</div>
                            <div class="text-gray-900">{{ project.beneficiaries_count|floatformat:0 }} people</div>
                        </div>
                        
                        <div>
                            <div class="text-sm font-medium text-gray-700">Contract Amount</div>
                            <div class="text-gray-900 text-lg font-semibold">₹{{ project.contract_amount|floatformat:0 }}</div>
                        </div>
                        
                        {% if project.project_document %}
                        <div>
                            <div class="text-sm font-medium text-gray-700 mb-2">Project Document</div>
                            <a href="{{ project.project_document.url }}" target="_blank" class="inline-flex items-center gradient-primary text-white px-4 py-2 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                                <i class="fas fa-download mr-2"></i>Download
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Related Actions -->
                <div class="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-200">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Get Involved</h3>
                    <div class="space-y-3">
                        <a href="{% url 'contact' %}" class="flex items-center p-3 bg-white rounded-lg hover:shadow-md transition-shadow">
                            <div class="w-8 h-8 gradient-primary rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-handshake text-white text-sm"></i>
                            </div>
                            <span class="text-gray-700 font-medium">Partner With Us</span>
                        </a>
                        
                        <a href="{% url 'projects' %}" class="flex items-center p-3 bg-white rounded-lg hover:shadow-md transition-shadow">
                            <div class="w-8 h-8 gradient-secondary rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-project-diagram text-white text-sm"></i>
                            </div>
                            <span class="text-gray-700 font-medium">View All Projects</span>
                        </a>
                        
                        <a href="{% url 'donate' %}" class="flex items-center p-3 bg-white rounded-lg hover:shadow-md transition-shadow">
                            <div class="w-8 h-8 gradient-accent rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-heart text-white text-sm"></i>
                            </div>
                            <span class="text-gray-700 font-medium">Support Our Work</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Projects -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Related Projects</h2>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                Explore other projects from the same donor or in similar focus areas
            </p>
        </div>
        
        <!-- This would typically be populated with related projects in a real implementation -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="bg-white rounded-xl shadow-lg p-6 text-center">
                <div class="w-16 h-16 gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-project-diagram text-white text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">More Projects</h3>
                <p class="text-gray-600 mb-4">Discover other impactful initiatives</p>
                <a href="{% url 'projects' %}" class="text-blue-600 hover:text-blue-800 font-medium">
                    View All <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>
            
            <div class="bg-white rounded-xl shadow-lg p-6 text-center">
                <div class="w-16 h-16 gradient-secondary rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-chart-line text-white text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Our Impact</h3>
                <p class="text-gray-600 mb-4">See the difference we're making</p>
                <a href="{% url 'impact' %}" class="text-blue-600 hover:text-blue-800 font-medium">
                    Learn More <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>
            
            <div class="bg-white rounded-xl shadow-lg p-6 text-center">
                <div class="w-16 h-16 gradient-accent rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-hands-helping text-white text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Get Involved</h3>
                <p class="text-gray-600 mb-4">Join our mission</p>
                <a href="{% url 'contact' %}" class="text-blue-600 hover:text-blue-800 font-medium">
                    Contact Us <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>
        </div>
    </div>
</section>
{% endblock %} 