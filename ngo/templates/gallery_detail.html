{% extends 'layouts/base.html' %}

{% block title %}{{ gallery.title }} - Gallery - {{ organization.name|default:"Youth Awareness Raising Center Nepal (YARCN)" }}{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="relative bg-gray-900 text-white py-20">
    {% if gallery.images.first %}
    <div class="absolute inset-0">
        <img src="{{ gallery.images.first.image.url }}" alt="{{ gallery.title }}" class="w-full h-full object-cover opacity-50">
    </div>
    {% else %}
    <div class="absolute inset-0 gradient-primary opacity-80"></div>
    {% endif %}
    
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="max-w-4xl">
            <nav class="text-blue-300 mb-4">
                <a href="{% url 'gallery' %}" class="hover:text-white transition-colors">Gallery</a>
                <span class="mx-2">/</span>
                <span class="text-white">{{ gallery.title }}</span>
            </nav>
            
            <div class="flex items-center mb-4">
                {% if gallery.is_featured %}
                <span class="bg-yellow-500 text-black px-3 py-1 rounded-full text-sm font-semibold mr-4">Featured Collection</span>
                {% endif %}
                <span class="text-lg text-gray-300">{{ gallery.created_date|date:"F d, Y" }}</span>
            </div>
            
            <h1 class="text-4xl md:text-5xl font-bold mb-6">{{ gallery.title }}</h1>
            
            {% if gallery.description %}
            <p class="text-xl text-gray-200 mb-8 leading-relaxed">
                {{ gallery.description }}
            </p>
            {% endif %}
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <i class="fas fa-images text-2xl mr-3"></i>
                        <div>
                            <div class="text-2xl font-bold">{{ images.count }}</div>
                            <div class="text-sm text-gray-300">Photos</div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <i class="fas fa-calendar text-2xl mr-3"></i>
                        <div>
                            <div class="text-lg font-bold">{{ gallery.created_date|date:"M Y" }}</div>
                            <div class="text-sm text-gray-300">Created</div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <i class="fas fa-eye text-2xl mr-3"></i>
                        <div>
                            <div class="text-lg font-bold">Public</div>
                            <div class="text-sm text-gray-300">Visibility</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Gallery Images Grid -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {% if images %}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {% for image in images %}
            <div class="group relative overflow-hidden rounded-xl shadow-lg card-hover cursor-pointer" onclick="openLightbox('{{ image.image.url }}', '{{ image.caption|default:gallery.title }}', {{ forloop.counter0 }})">
                <div class="aspect-square overflow-hidden">
                    <img src="{{ image.image.url }}" alt="{{ image.caption|default:gallery.title }}" class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                </div>
                
                <!-- Overlay -->
                <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-60 transition-all duration-300 flex items-center justify-center">
                    <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-center">
                        <i class="fas fa-search-plus text-white text-3xl mb-2"></i>
                        <p class="text-white text-sm font-medium">View Full Size</p>
                    </div>
                </div>
                
                <!-- Caption -->
                {% if image.caption %}
                <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black via-black/50 to-transparent p-4">
                    <p class="text-white text-sm font-medium">{{ image.caption }}</p>
                </div>
                {% endif %}
                
                <!-- Image Number -->
                <div class="absolute top-3 right-3">
                    <span class="bg-black bg-opacity-70 text-white px-2 py-1 rounded-full text-xs font-medium">
                        {{ forloop.counter }}
                    </span>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <!-- Download All Button -->
        {% comment %} <div class="text-center mt-12">
            <button class="gradient-primary text-white px-8 py-3 rounded-full font-semibold hover:shadow-lg transition-all duration-300" onclick="downloadAllImages()">
                <i class="fas fa-download mr-2"></i>Download Collection
            </button>
        </div> {% endcomment %}
        {% else %}
        <div class="text-center py-16">
            <div class="w-24 h-24 gradient-primary rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-images text-white text-3xl"></i>
            </div>
            <h3 class="text-2xl font-semibold text-gray-900 mb-4">No Images Yet</h3>
            <p class="text-gray-600 max-w-md mx-auto mb-8">
                This collection is being prepared. Check back soon for photos from this collection.
            </p>
            <a href="{% url 'gallery' %}" class="gradient-primary text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                <i class="fas fa-arrow-left mr-2"></i>Back to Gallery
            </a>
        </div>
        {% endif %}
    </div>
</section>

<!-- Related Collections -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Other Collections</h2>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                Explore more of our visual stories
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="bg-white rounded-xl shadow-lg p-6 text-center card-hover">
                <div class="w-16 h-16 gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-images text-white text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">All Collections</h3>
                <p class="text-gray-600 mb-4">Browse all our photo collections</p>
                <a href="{% url 'gallery' %}" class="text-blue-600 hover:text-blue-800 font-medium">
                    View Gallery <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>
            
            <div class="bg-white rounded-xl shadow-lg p-6 text-center card-hover">
                <div class="w-16 h-16 gradient-secondary rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-project-diagram text-white text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Our Projects</h3>
                <p class="text-gray-600 mb-4">See the work behind the photos</p>
                <a href="{% url 'projects' %}" class="text-blue-600 hover:text-blue-800 font-medium">
                    View Projects <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>
            
            <div class="bg-white rounded-xl shadow-lg p-6 text-center card-hover">
                <div class="w-16 h-16 gradient-accent rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-chart-line text-white text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Our Impact</h3>
                <p class="text-gray-600 mb-4">Discover our achievements</p>
                <a href="{% url 'impact' %}" class="text-blue-600 hover:text-blue-800 font-medium">
                    See Impact <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Lightbox Modal -->
<div id="lightbox" class="fixed inset-0 bg-black bg-opacity-95 z-50 hidden flex items-center justify-center p-4">
    <div class="relative max-w-6xl max-h-full w-full">
        <!-- Navigation -->
        <button onclick="previousImage()" class="absolute left-4 top-1/2 transform -translate-y-1/2 text-white text-2xl z-10 w-12 h-12 bg-black bg-opacity-50 rounded-full flex items-center justify-center hover:bg-opacity-75 transition-all">
            <i class="fas fa-chevron-left"></i>
        </button>
        
        <button onclick="nextImage()" class="absolute right-4 top-1/2 transform -translate-y-1/2 text-white text-2xl z-10 w-12 h-12 bg-black bg-opacity-50 rounded-full flex items-center justify-center hover:bg-opacity-75 transition-all">
            <i class="fas fa-chevron-right"></i>
        </button>
        
        <!-- Close Button -->
        <button onclick="closeLightbox()" class="absolute top-4 right-4 text-white text-2xl z-10 w-12 h-12 bg-black bg-opacity-50 rounded-full flex items-center justify-center hover:bg-opacity-75 transition-all">
            <i class="fas fa-times"></i>
        </button>
        
        <!-- Image Counter -->
        <div class="absolute top-4 left-4 text-white z-10 bg-black bg-opacity-50 px-3 py-1 rounded-full text-sm">
            <span id="image-counter">1 of {{ images.count }}</span>
        </div>
        
        <!-- Main Image -->
        <img id="lightbox-image" src="" alt="" class="max-w-full max-h-full object-contain mx-auto">
        
        <!-- Caption -->
        <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black via-black/50 to-transparent p-6">
            <p id="lightbox-caption" class="text-white text-center text-lg"></p>
        </div>
    </div>
</div>

<!-- JavaScript for Lightbox -->
<script>
let currentImageIndex = 0;
const images = [
    {% for image in images %}
    {
        src: "{{ image.image.url }}",
        caption: "{{ image.caption|default:gallery.title|escapejs }}"
    }{% if not forloop.last %},{% endif %}
    {% endfor %}
];

function openLightbox(imageSrc, caption, index) {
    currentImageIndex = index;
    updateLightboxImage();
    
    const lightbox = document.getElementById('lightbox');
    lightbox.classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function closeLightbox() {
    const lightbox = document.getElementById('lightbox');
    lightbox.classList.add('hidden');
    document.body.style.overflow = 'auto';
}

function updateLightboxImage() {
    if (images.length === 0) return;
    
    const lightboxImage = document.getElementById('lightbox-image');
    const lightboxCaption = document.getElementById('lightbox-caption');
    const imageCounter = document.getElementById('image-counter');
    
    lightboxImage.src = images[currentImageIndex].src;
    lightboxCaption.textContent = images[currentImageIndex].caption;
    imageCounter.textContent = `${currentImageIndex + 1} of ${images.length}`;
}

function nextImage() {
    if (images.length === 0) return;
    currentImageIndex = (currentImageIndex + 1) % images.length;
    updateLightboxImage();
}

function previousImage() {
    if (images.length === 0) return;
    currentImageIndex = (currentImageIndex - 1 + images.length) % images.length;
    updateLightboxImage();
}

function downloadAllImages() {
    // This is a placeholder function
    // In a real implementation, you'd need backend support for creating zip files
    alert('Download functionality would be implemented with backend support to create a zip file of all images.');
}

// Keyboard navigation
document.addEventListener('keydown', function(e) {
    if (document.getElementById('lightbox').classList.contains('hidden')) return;
    
    switch(e.key) {
        case 'Escape':
            closeLightbox();
            break;
        case 'ArrowLeft':
            previousImage();
            break;
        case 'ArrowRight':
            nextImage();
            break;
    }
});

// Close lightbox when clicking outside the image
document.getElementById('lightbox').addEventListener('click', function(e) {
    if (e.target === this) {
        closeLightbox();
    }
});
</script>
{% endblock %} 