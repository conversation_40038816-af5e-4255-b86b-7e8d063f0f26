<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>OH-OH</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: 'Georgia', serif;
        background: linear-gradient(135deg, #1a1a1a 0%, #2c2c2c 50%, #1f1f1f 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #e5e5e5;
      }
      
      .status-container {
        background: rgba(40, 40, 40, 0.95);
        border: 1px solid #404040;
        border-radius: 12px;
        padding: 80px 60px;
        text-align: center;
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
        max-width: 700px;
        width: 90%;
        backdrop-filter: blur(10px);
      }
      
      .status-message {
        font-size: 1.8rem;
        color: #e5e5e5;
        line-height: 1.8;
        font-weight: 300;
        letter-spacing: 0.5px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }
      
      @media (max-width: 768px) {
        .status-container {
          padding: 60px 40px;
        }
      
        .status-message {
          font-size: 1.5rem;
        }
      }
      
      @media (max-width: 480px) {
        .status-container {
          padding: 40px 20px;
        }
      
        .status-message {
          font-size: 1.3rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="status-container">
      <div class="status-message">
        {{ status_message|default:"Site is currently under maintenance." }}
      </div>
    </div>

    <script>
      // Auto refresh every 30 seconds to check if site is back online
      setTimeout(function () {
        location.reload()
      }, 30000)
    </script>
  </body>
</html>
