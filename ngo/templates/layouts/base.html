<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ organization.name|default:"Youth Awareness Raising Center Nepal (YARCN)" }}{% endblock %}</title>
    <meta name="description" content="{% block meta_description %}{{ organization.tagline|default:'Empowering youth for socio-economic transformation in Nepal' }}{% endblock %}">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS with theme variables -->
    <style>
        :root {
            /* Primary Colors */
            --primary-50: #eff6ff;
            --primary-100: #dbeafe;
            --primary-200: #bfdbfe;
            --primary-300: #93c5fd;
            --primary-400: #60a5fa;
            --primary-500: #3b82f6;
            --primary-600: #2563eb;
            --primary-700: #1d4ed8;
            --primary-800: #1e40af;
            --primary-900: #1e3a8a;
            
            /* Secondary Colors */
            --secondary-50: #f0f9ff;
            --secondary-100: #e0f2fe;
            --secondary-200: #bae6fd;
            --secondary-300: #7dd3fc;
            --secondary-400: #38bdf8;
            --secondary-500: #0ea5e9;
            --secondary-600: #0284c7;
            --secondary-700: #0369a1;
            --secondary-800: #075985;
            --secondary-900: #0c4a6e;
            
            /* Accent Colors */
            --accent-50: #ecfdf5;
            --accent-100: #d1fae5;
            --accent-200: #a7f3d0;
            --accent-300: #6ee7b7;
            --accent-400: #34d399;
            --accent-500: #10b981;
            --accent-600: #059669;
            --accent-700: #047857;
            --accent-800: #065f46;
            --accent-900: #064e3b;
            
            /* Gradients */
            --gradient-primary: linear-gradient(135deg, var(--primary-500) 0%, var(--secondary-500) 100%);
            --gradient-secondary: linear-gradient(135deg, var(--secondary-400) 0%, var(--primary-400) 100%);
            --gradient-accent: linear-gradient(135deg, var(--accent-400) 0%, var(--primary-400) 100%);
            
            /* Shadows */
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
        }
        
        /* Custom utility classes */
        .gradient-primary { background: var(--gradient-primary); }
        .gradient-secondary { background: var(--gradient-secondary); }
        .gradient-accent { background: var(--gradient-accent); }
        
        .text-primary-500 { color: var(--primary-500); }
        .text-secondary-500 { color: var(--secondary-500); }
        .text-accent-500 { color: var(--accent-500); }
        
        .bg-primary-50 { background-color: var(--primary-50); }
        .bg-primary-500 { background-color: var(--primary-500); }
        .bg-secondary-500 { background-color: var(--secondary-500); }
        
        /* Smooth scrolling */
        html { scroll-behavior: smooth; }
        
        /* Custom animations */
        .fade-in { animation: fadeIn 0.6s ease-in; }
        .slide-up { animation: slideUp 0.6s ease-out; }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes slideUp {
            from { transform: translateY(30px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        
        /* Navigation hover effects */
        .nav-link {
            position: relative;
            transition: all 0.3s ease;
        }
        
        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -4px;
            left: 50%;
            background: var(--gradient-primary);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }
        
        .nav-link:hover::after,
        .nav-link.active::after {
            width: 100%;
        }
        
        /* Card hover effects */
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-xl);
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body class="bg-gray-50 font-sans">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="{% url 'home' %}" class="flex items-center space-x-3">
                        <div class="w-10 h-10 gradient-primary rounded-lg flex items-center justify-center">
                            {% if organization.logo %}
                                <img src="{{ organization.logo.url }}" alt="YARCN Logo" class="w-full h-full object-cover rounded-lg">
                            {% else %}
                                <i class="fas fa-hands-helping text-white text-lg"></i>
                            {% endif %}
                        </div>
                        <div>
                            <div class="text-xl font-bold text-gray-900">YARCN</div>
                            <div class="text-xs text-gray-600 hidden sm:block">Youth Awareness Raising Center</div>
                        </div>
                    </a>
                </div>
                
                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-4">
                    <a href="{% url 'home' %}" class="nav-link {% if request.resolver_match.url_name == 'home' %}active{% endif %} text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium">Home</a>
                    <a href="{% url 'about' %}" class="nav-link {% if request.resolver_match.url_name == 'about' %}active{% endif %} text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium">About</a>
                    <a href="{% url 'programs' %}" class="nav-link {% if request.resolver_match.url_name == 'programs' %}active{% endif %} text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium">Programs</a>
                    <a href="{% url 'projects' %}" class="nav-link {% if request.resolver_match.url_name == 'projects' %}active{% endif %} text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium">Projects</a>
                    <a href="{% url 'team' %}" class="nav-link {% if request.resolver_match.url_name == 'team' %}active{% endif %} text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium">Team</a>
                    <a href="{% url 'gallery' %}" class="nav-link {% if request.resolver_match.url_name == 'gallery' %}active{% endif %} text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium">Gallery</a>
                    <a href="{% url 'impact' %}" class="nav-link {% if request.resolver_match.url_name == 'impact' %}active{% endif %} text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium">Impact</a>
                    <a href="{% url 'resources' %}" class="nav-link {% if request.resolver_match.url_name == 'resources' %}active{% endif %} text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium">Resources</a>
                    <a href="{% url 'contact' %}" class="nav-link {% if request.resolver_match.url_name == 'contact' %}active{% endif %} text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium">Contact</a>
                    
                    <!-- Donate Button -->
                    <a href="{% url 'donate' %}" class="gradient-primary text-white px-6 py-2 rounded-full text-sm font-medium hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                        <i class="fas fa-heart mr-2"></i>Donate
                    </a>
                </div>
                
                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button type="button" class="mobile-menu-button text-gray-500 hover:text-gray-600 focus:outline-none focus:text-gray-600" aria-label="toggle menu">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile Navigation -->
        <div class="mobile-menu hidden md:hidden bg-white border-t border-gray-200">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="{% url 'home' %}" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600 hover:bg-gray-50">Home</a>
                <a href="{% url 'about' %}" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600 hover:bg-gray-50">About</a>
                <a href="{% url 'programs' %}" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600 hover:bg-gray-50">Programs</a>
                <a href="{% url 'projects' %}" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600 hover:bg-gray-50">Projects</a>
                <a href="{% url 'team' %}" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600 hover:bg-gray-50">Team</a>
                <a href="{% url 'gallery' %}" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600 hover:bg-gray-50">Gallery</a>
                <a href="{% url 'impact' %}" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600 hover:bg-gray-50">Impact</a>
                <a href="{% url 'resources' %}" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600 hover:bg-gray-50">Resources</a>
                <a href="{% url 'contact' %}" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600 hover:bg-gray-50">Contact</a>
                <a href="{% url 'donate' %}" class="block mx-3 my-2 gradient-primary text-white px-4 py-2 rounded-lg text-center font-medium">
                    <i class="fas fa-heart mr-2"></i>Donate
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Organization Info -->
                <div class="md:col-span-2">
                    <div class="flex items-center space-x-3 mb-4">
                        <div class="w-12 h-12 gradient-primary rounded-lg flex items-center justify-center">
                            {% if organization.logo %}
                                <img src="{{ organization.logo.url }}" alt="YARCN Logo" class="w-full h-full object-cover rounded-lg">
                            {% else %}
                                <i class="fas fa-hands-helping text-white text-xl"></i>
                            {% endif %}
                        </div>
                        <div>
                            <div class="text-2xl font-bold">YARCN</div>
                            <div class="text-gray-400">Youth Awareness Raising Center Nepal</div>
                        </div>
                    </div>
                    <p class="text-gray-300 mb-4">Empowering youth for socio-economic transformation in Nepal since 2003. Working with marginalized communities to create sustainable change.</p>
                    {% comment %} <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition-colors"><i class="fab fa-facebook text-xl"></i></a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors"><i class="fab fa-twitter text-xl"></i></a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors"><i class="fab fa-linkedin text-xl"></i></a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors"><i class="fab fa-youtube text-xl"></i></a>
                    </div> {% endcomment %}
                </div>
                
                <!-- Quick Links -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="{% url 'about' %}" class="text-gray-300 hover:text-white transition-colors">About Us</a></li>
                        <li><a href="{% url 'programs' %}" class="text-gray-300 hover:text-white transition-colors">Programs</a></li>
                        <li><a href="{% url 'projects' %}" class="text-gray-300 hover:text-white transition-colors">Projects</a></li>
                        <li><a href="{% url 'gallery' %}" class="text-gray-300 hover:text-white transition-colors">Gallery</a></li>
                        <li><a href="{% url 'impact' %}" class="text-gray-300 hover:text-white transition-colors">Impact</a></li>
                        <li><a href="{% url 'resources' %}" class="text-gray-300 hover:text-white transition-colors">Resources</a></li>
                    </ul>
                </div>
                
                <!-- Contact Info -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Contact Info</h3>
                    <div class="space-y-2 text-gray-300">
                        <div class="flex items-center">
                            <i class="fas fa-map-marker-alt w-4 mr-3"></i>
                            <span>Bheri Municipality-4, Jajarkot</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-phone w-4 mr-3"></i>
                            <span>089-430167</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-envelope w-4 mr-3"></i>
                            <span><EMAIL></span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-globe w-4 mr-3"></i>
                            <span>www.yarcn.org.np</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 Youth Awareness Raising Center Nepal (YARCN). All rights reserved.</p>
                <p class="mt-2">Registration No: 561 | Social Welfare Council: 14662 | VAT: 301763875</p>
                <p class="mt-2">Design & Developed💙 | Imagine Infotech| नेपालमा बनेको</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript for mobile menu -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuButton = document.querySelector('.mobile-menu-button');
            const mobileMenu = document.querySelector('.mobile-menu');
            
            mobileMenuButton.addEventListener('click', function() {
                mobileMenu.classList.toggle('hidden');
            });
            
            // Close mobile menu when clicking on a link
            const mobileMenuLinks = mobileMenu.querySelectorAll('a');
            mobileMenuLinks.forEach(link => {
                link.addEventListener('click', function() {
                    mobileMenu.classList.add('hidden');
                });
            });
        });
        
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>