{% extends 'layouts/base.html' %}

{% block title %}Our Team - {{ organization.name|default:"Youth Awareness Raising Center Nepal (YARCN)" }}{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="gradient-primary text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-bold mb-6">Our Team</h1>
            <p class="text-xl text-blue-100 max-w-3xl mx-auto">
                Meet the dedicated individuals driving positive change in Nepal's communities
            </p>
        </div>
    </div>
</section>

<!-- Board Members -->
{% if board_members %}
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Executive Board</h2>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                Experienced leaders guiding our organization's strategic direction
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {% for member in board_members %}
            <div class="bg-white border border-gray-200 rounded-xl shadow-lg overflow-hidden card-hover">
                {% if member.photo %}
                <div class="h-64 overflow-hidden">
                    <img src="{{ member.photo.url }}" alt="{{ member.name }}" class="w-full h-full object-cover">
                </div>
                {% else %}
                <div class="h-64 gradient-primary flex items-center justify-center">
                    <i class="fas fa-user text-white text-6xl"></i>
                </div>
                {% endif %}
                
                <div class="p-6">
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ member.name }}</h3>
                    <p class="text-blue-600 font-medium mb-3">{{ member.position }}</p>
                    
                    <div class="space-y-2 text-sm text-gray-600 mb-4">
                        <div class="flex items-center">
                            <i class="fas fa-graduation-cap w-4 mr-3 text-blue-500"></i>
                            <span>{{ member.qualification }}</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-map-marker-alt w-4 mr-3 text-green-500"></i>
                            <span>{{ member.address }}</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-clock w-4 mr-3 text-orange-500"></i>
                            <span>{{ member.experience_years }} years experience</span>
                        </div>
                    </div>
                    
                    <div class="pt-4 border-t border-gray-200">
                        <h4 class="text-sm font-medium text-gray-900 mb-2">Expertise</h4>
                        <p class="text-sm text-gray-600">{{ member.expertise }}</p>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

<!-- Staff Members -->
{% if staff_members %}
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Staff</h2>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                Dedicated professionals implementing programs and serving communities
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {% for staff in staff_members %}
            <div class="bg-white rounded-xl shadow-lg overflow-hidden card-hover">
                {% if staff.photo %}
                <div class="h-48 overflow-hidden">
                    <img src="{{ staff.photo.url }}" alt="{{ staff.name }}" class="w-full h-full object-cover">
                </div>
                {% else %}
                <div class="h-48 gradient-secondary flex items-center justify-center">
                    <i class="fas fa-user text-white text-4xl"></i>
                </div>
                {% endif %}
                
                <div class="p-4">
                    <h4 class="text-lg font-semibold text-gray-900 mb-1">{{ staff.name }}</h4>
                    <p class="text-blue-600 text-sm font-medium mb-2">{{ staff.position }}</p>
                    <p class="text-xs text-gray-600 mb-2">{{ staff.qualification }}</p>
                    <p class="text-xs text-gray-500">{{ staff.experience_duration }}</p>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

<!-- Team Stats -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Team Strength</h2>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                Our diverse team brings together expertise from various fields
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div class="text-center">
                <div class="w-16 h-16 gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-users-cog text-white text-2xl"></i>
                </div>
                <div class="text-3xl font-bold text-gray-900 mb-2">{{ board_members|length }}</div>
                <div class="text-gray-600">Board Members</div>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 gradient-secondary rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-user-tie text-white text-2xl"></i>
                </div>
                <div class="text-3xl font-bold text-gray-900 mb-2">{{ staff_members|length }}</div>
                <div class="text-gray-600">Active Staff</div>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 gradient-accent rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-graduation-cap text-white text-2xl"></i>
                </div>
                <div class="text-3xl font-bold text-gray-900 mb-2">21+</div>
                <div class="text-gray-600">Years Combined Experience</div>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-handshake text-white text-2xl"></i>
                </div>
                <div class="text-3xl font-bold text-gray-900 mb-2">100%</div>
                <div class="text-gray-600">Commitment</div>
            </div>
        </div>
    </div>
</section>

<!-- Join Our Team -->
<section class="py-16 gradient-primary text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-bold mb-6">Join Our Team</h2>
        <p class="text-xl mb-8 text-blue-100 max-w-3xl mx-auto">
            Be part of our mission to create positive change in Nepal. We're always looking for passionate individuals.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{% url 'contact' %}" class="bg-white text-blue-600 px-8 py-3 rounded-full font-semibold hover:bg-blue-50 transition-colors">
                <i class="fas fa-briefcase mr-2"></i>Career Opportunities
            </a>
            <a href="{% url 'contact' %}" class="border-2 border-white text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                <i class="fas fa-hands-helping mr-2"></i>Volunteer With Us
            </a>
        </div>
    </div>
</section>
{% endblock %} 
 