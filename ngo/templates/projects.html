{% extends 'layouts/base.html' %}

{% block title %}Projects - {{ organization.name|default:"Youth Awareness Raising Center Nepal (YARCN)" }}{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="gradient-primary text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-bold mb-6">Our Projects</h1>
            <p class="text-xl text-blue-100 max-w-3xl mx-auto">
                Discover the initiatives that are making a real difference in communities across Nepal
            </p>
        </div>
    </div>
</section>

<!-- Project Statistics -->
<section class="py-12 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="text-center">
                <div class="w-16 h-16 gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-project-diagram text-white text-2xl"></i>
                </div>
                <div class="text-3xl font-bold text-gray-900 mb-2">{{ total_projects }}</div>
                <div class="text-gray-600">Total Projects</div>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 gradient-secondary rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-users text-white text-2xl"></i>
                </div>
                <div class="text-3xl font-bold text-gray-900 mb-2">{{ total_beneficiaries|floatformat:0 }}</div>
                <div class="text-gray-600">Beneficiaries</div>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 gradient-accent rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-coins text-white text-2xl"></i>
                </div>
                <div class="text-3xl font-bold text-gray-900 mb-2">₹{{ total_investment|floatformat:0 }}M</div>
                <div class="text-gray-600">Total Investment</div>
            </div>
        </div>
    </div>
</section>

<!-- Filters -->
<section class="py-8 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white p-6 rounded-xl shadow-lg">
            <form method="GET" class="flex flex-col md:flex-row gap-4">
                <div class="flex-1">
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select name="status" id="status" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="all" {% if status_filter == 'all' %}selected{% endif %}>All Status</option>
                        <option value="ongoing" {% if status_filter == 'ongoing' %}selected{% endif %}>Ongoing</option>
                        <option value="completed" {% if status_filter == 'completed' %}selected{% endif %}>Completed</option>
                        <option value="planned" {% if status_filter == 'planned' %}selected{% endif %}>Planned</option>
                    </select>
                </div>
                
                <div class="flex-1">
                    <label for="donor" class="block text-sm font-medium text-gray-700 mb-2">Donor Agency</label>
                    <select name="donor" id="donor" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="all" {% if donor_filter == 'all' %}selected{% endif %}>All Donors</option>
                        {% for donor in donors %}
                        <option value="{{ donor }}" {% if donor_filter == donor %}selected{% endif %}>{{ donor }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="flex items-end">
                    <button type="submit" class="gradient-primary text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                        <i class="fas fa-filter mr-2"></i>Filter
                    </button>
                </div>
            </form>
        </div>
    </div>
</section>

<!-- Projects Grid -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {% if projects %}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {% for project in projects %}
            <div class="bg-white border border-gray-200 rounded-xl shadow-lg overflow-hidden card-hover">
                {% if project.featured_image %}
                <div class="h-48 overflow-hidden">
                    <img src="{{ project.featured_image.url }}" alt="{{ project.title }}" class="w-full h-full object-cover">
                </div>
                {% else %}
                <div class="h-48 gradient-primary flex items-center justify-center">
                    <i class="fas fa-project-diagram text-white text-4xl"></i>
                </div>
                {% endif %}
                
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <span class="px-3 py-1 bg-{% if project.status == 'ongoing' %}green{% elif project.status == 'completed' %}blue{% else %}yellow{% endif %}-100 text-{% if project.status == 'ongoing' %}green{% elif project.status == 'completed' %}blue{% else %}yellow{% endif %}-800 text-xs font-semibold rounded-full uppercase tracking-wide">
                            {{ project.status }}
                        </span>
                        <span class="text-sm text-gray-500">{{ project.start_date.year }}-{{ project.end_date.year }}</span>
                    </div>
                    
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">{{ project.title }}</h3>
                    <p class="text-gray-600 mb-4 text-sm">{{ project.core_activities|truncatewords:20 }}</p>
                    
                    <div class="space-y-2 text-sm text-gray-600 mb-4">
                        <div class="flex items-center">
                            <i class="fas fa-handshake w-4 mr-3 text-blue-500"></i>
                            <span>{{ project.donor_agency }}</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-users w-4 mr-3 text-green-500"></i>
                            <span>{{ project.beneficiaries_count }} beneficiaries</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-map-marker-alt w-4 mr-3 text-red-500"></i>
                            <span>{{ project.location|truncatewords:5 }}</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-coins w-4 mr-3 text-yellow-500"></i>
                            <span>₹{{ project.contract_amount|floatformat:0 }}</span>
                        </div>
                    </div>
                    
                    <div class="pt-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <a href="{% url 'project_detail' project.id %}" class="text-blue-600 hover:text-blue-800 font-medium">
                                View Details <i class="fas fa-arrow-right ml-1"></i>
                            </a>
                            
                            {% if project.project_document %}
                            <a href="{{ project.project_document.url }}" target="_blank" class="inline-flex items-center text-green-600 hover:text-green-800 font-medium text-sm">
                                <i class="fas fa-download mr-1"></i>Document
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-12">
            <div class="w-24 h-24 gradient-primary rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-search text-white text-3xl"></i>
            </div>
            <h3 class="text-2xl font-semibold text-gray-900 mb-4">No Projects Found</h3>
            <p class="text-gray-600 max-w-md mx-auto">
                No projects match your current filters. Try adjusting your search criteria.
            </p>
        </div>
        {% endif %}
    </div>
</section>

<!-- Timeline Section -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Project Timeline</h2>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                Our journey of impactful projects over the years
            </p>
        </div>
        
        <div class="space-y-8">
            {% for project in projects|slice:":5" %}
            <div class="flex items-center">
                <div class="w-4 h-4 gradient-primary rounded-full"></div>
                <div class="ml-6 bg-white p-6 rounded-lg shadow-lg flex-1">
                    <div class="flex justify-between items-start mb-2">
                        <h4 class="text-lg font-semibold text-gray-900">{{ project.title }}</h4>
                        <span class="text-sm text-gray-500">{{ project.start_date.year }}</span>
                    </div>
                    <p class="text-gray-600 text-sm">{{ project.donor_agency }} • {{ project.beneficiaries_count }} beneficiaries</p>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Call to Action -->
<section class="py-16 gradient-primary text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-bold mb-6">Partner With Us</h2>
        <p class="text-xl mb-8 text-blue-100 max-w-3xl mx-auto">
            Join us in creating more impactful projects that transform communities
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{% url 'contact' %}" class="bg-white text-blue-600 px-8 py-3 rounded-full font-semibold hover:bg-blue-50 transition-colors">
                <i class="fas fa-handshake mr-2"></i>Collaborate
            </a>
            <a href="{% url 'impact' %}" class="border-2 border-white text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                <i class="fas fa-chart-line mr-2"></i>See Our Impact
            </a>
        </div>
    </div>
</section>
{% endblock %} 