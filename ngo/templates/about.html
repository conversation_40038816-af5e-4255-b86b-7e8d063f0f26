{% extends 'layouts/base.html' %}

{% block title %}About Us - {{ organization.name|default:"Youth Awareness Raising Center Nepal (YARCN)" }}{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="gradient-primary text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-bold mb-6">About YARCN</h1>
            <p class="text-xl text-blue-100 max-w-3xl mx-auto">
                Learn about our journey, mission, and commitment to empowering communities in Nepal
            </p>
        </div>
    </div>
</section>

<!-- Organization Introduction -->
{% if organization %}
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">Our Story</h2>
                <p class="text-lg text-gray-600 mb-6 leading-relaxed">
                    Youth Awareness Rising Centre Nepal (YARCN) was established on 2059-11-02 B.S. (2003) to help improve the socio-economic and cultural situation of backward and poor people. This non-partisan, non-discriminatory, and non-religious organization serves the marginalized, disadvantaged, and resource-poor community.
                </p>
                <p class="text-lg text-gray-600 mb-6 leading-relaxed">
                    YARCN has been working for the promotion of health education, peacebuilding, community development, skill development of the marginalized youth, saving credit for the economic sustainability of socially and culturally backward communities since its establishment.
                </p>
                <div class="grid grid-cols-2 gap-4">
                    <div class="text-center p-4 bg-blue-50 rounded-lg">
                        <div class="text-2xl font-bold text-blue-600">2003</div>
                        <div class="text-sm text-gray-600">Established</div>
                    </div>
                    <div class="text-center p-4 bg-blue-50 rounded-lg">
                        <div class="text-2xl font-bold text-blue-600">21+</div>
                        <div class="text-sm text-gray-600">Years Serving</div>
                    </div>
                </div>
                
                {% if organization.profile_document %}
                <div class="mt-6">
                    <a href="{{ organization.profile_document.url }}" target="_blank" class="inline-flex items-center gradient-primary text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                        <i class="fas fa-download mr-2"></i>Download Organization Profile
                    </a>
                </div>
                {% endif %}
            </div>
            
            <div class="space-y-6">
                <div class="bg-gray-50 p-8 rounded-xl">
                    <div class="w-12 h-12 gradient-primary rounded-lg flex items-center justify-center mb-4">
                        <i class="fas fa-eye text-white text-xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Our Vision</h3>
                    <p class="text-gray-600">{{ organization.vision }}</p>
                </div>
                
                <div class="bg-gray-50 p-8 rounded-xl">
                    <div class="w-12 h-12 gradient-secondary rounded-lg flex items-center justify-center mb-4">
                        <i class="fas fa-bullseye text-white text-xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Our Mission</h3>
                    <p class="text-gray-600">{{ organization.mission }}</p>
                </div>
                
                <div class="bg-gray-50 p-8 rounded-xl">
                    <div class="w-12 h-12 gradient-accent rounded-lg flex items-center justify-center mb-4">
                        <i class="fas fa-target text-white text-xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Our Goal</h3>
                    <p class="text-gray-600">{{ organization.goal }}</p>
                </div>
            </div>
        </div>
    </div>
</section>
{% endif %}

<!-- Core Values -->
{% if core_values %}
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Core Values</h2>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                The principles that guide our work and define our organizational culture
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {% for value in core_values %}
            <div class="bg-white p-8 rounded-xl shadow-lg card-hover">
                <div class="w-16 h-16 gradient-primary rounded-full flex items-center justify-center mb-6">
                    <i class="fas fa-{{ value.icon|default:'star' }} text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-4">{{ value.title }}</h3>
                <p class="text-gray-600 leading-relaxed">{{ value.description }}</p>
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

<!-- Objectives -->
{% if objectives %}
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Objectives</h2>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                Strategic objectives that drive our programs and initiatives
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            {% for objective in objectives %}
            <div class="flex items-start p-6 bg-gray-50 rounded-xl card-hover">
                <div class="w-8 h-8 gradient-primary rounded-full flex items-center justify-center mr-4 mt-1 flex-shrink-0">
                    <span class="text-white font-semibold text-sm">{{ forloop.counter }}</span>
                </div>
                <p class="text-gray-700 leading-relaxed">{{ objective.description }}</p>
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

<!-- Operational Approaches -->
{% if approaches %}
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Operational Approaches</h2>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                Our strategic approaches to community development and social transformation
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {% for approach in approaches %}
            <div class="bg-white p-6 rounded-xl shadow-lg card-hover">
                <div class="w-12 h-12 gradient-secondary rounded-lg flex items-center justify-center mb-4">
                    <i class="fas fa-cog text-white text-lg"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3">{{ approach.title }}</h3>
                <p class="text-gray-600 text-sm">{{ approach.description }}</p>
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

<!-- Registration & Legal Information -->
{% if registrations %}
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Legal Registration</h2>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                Our official registrations and legal compliance
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            {% for registration in registrations %}
            <div class="bg-gray-50 p-6 rounded-xl">
                <div class="flex items-center mb-4">
                    <div class="w-10 h-10 gradient-primary rounded-lg flex items-center justify-center mr-4">
                        <i class="fas fa-certificate text-white"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold text-gray-900">{{ registration.office_name }}</h3>
                        <p class="text-sm text-gray-600">Registration No: {{ registration.registration_number }}</p>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <p><strong>Registration Date:</strong> {{ registration.registration_date|date:"F d, Y" }}</p>
                    {% if registration.renewal_date %}
                    <p><strong>Renewal Date:</strong> {{ registration.renewal_date|date:"F d, Y" }}</p>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

<!-- Membership Information -->
{% if membership_data %}
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Membership Overview</h2>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                Our diverse and inclusive membership base
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            {% for membership in membership_data %}
            <div class="bg-white p-8 rounded-xl shadow-lg">
                <h3 class="text-2xl font-bold text-gray-900 mb-6">{{ membership.get_membership_type_display }}</h3>
                
                <!-- Gender Distribution -->
                <div class="mb-6">
                    <h4 class="text-lg font-semibold text-gray-800 mb-3">Gender Distribution</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Male</span>
                            <span class="font-semibold text-blue-600">{{ membership.male_count }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Female</span>
                            <span class="font-semibold text-pink-600">{{ membership.female_count }}</span>
                        </div>
                        <div class="flex justify-between items-center border-t pt-2">
                            <span class="font-semibold text-gray-800">Total</span>
                            <span class="font-bold text-gray-900">{{ membership.total_count }}</span>
                        </div>
                    </div>
                </div>
                
                <!-- Ethnicity Distribution -->
                <div>
                    <h4 class="text-lg font-semibold text-gray-800 mb-3">Ethnicity Distribution</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Dalit</span>
                            <span class="font-semibold text-green-600">{{ membership.dalit_count }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Janajati</span>
                            <span class="font-semibold text-orange-600">{{ membership.janajati_count }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Others</span>
                            <span class="font-semibold text-purple-600">{{ membership.others_count }}</span>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

<!-- Call to Action -->
<section class="py-16 gradient-primary text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-bold mb-6">Join Our Mission</h2>
        <p class="text-xl mb-8 text-blue-100 max-w-3xl mx-auto">
            Be part of our journey in creating positive change in communities across Nepal
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{% url 'contact' %}" class="bg-white text-blue-600 px-8 py-3 rounded-full font-semibold hover:bg-blue-50 transition-colors">
                <i class="fas fa-envelope mr-2"></i>Get in Touch
            </a>
            <a href="{% url 'programs' %}" class="border-2 border-white text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                <i class="fas fa-arrow-right mr-2"></i>Explore Programs
            </a>
        </div>
    </div>
</section>
{% endblock %} 