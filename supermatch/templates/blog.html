{% extends "base.html" %}

{% block content %}
    <!-- Page Title -->
    <div class="page-title dark-background" style="background-image: url(assets/img/page-title-bg.jpg);">
      <div class="container position-relative">
        <h1>Blog</h1>
        <nav class="breadcrumbs">
          <ol>
            <li><a href="{% url 'index' %}">Home</a></li>
            <li class="current">Blog</li>
          </ol>
        </nav>
      </div>
    </div><!-- End Page Title -->

    <!-- Blog Posts Section -->
    <section id="blog-posts" class="blog-posts section">
      <div class="container">
        <div class="row">
          <!-- Blog Posts Column -->
          <div class="col-lg-8">
            <div class="row gy-4">
              {% if blog_posts %}
                {% for post in blog_posts %}
                  <div class="col-lg-6">
                    <article class="position-relative h-100">
                      <div class="post-img position-relative overflow-hidden">
                        {% if post.image %}
                        <img src="{{ post.image.url }}" class="img-fluid" alt="{{ post.title }}">
                        {% endif %}
                        <span class="post-date">{{ post.created_at|date:"F d" }}</span>
                      </div>
                      <div class="post-content d-flex flex-column">
                        <h3 class="post-title">{{ post.title }}</h3>
                        <div class="meta d-flex align-items-center">
                          <div class="d-flex align-items-center">
                            <i class="bi bi-person"></i> <span class="ps-2">{{ post.author }}</span>
                          </div>
                          <span class="px-3 text-black-50">/</span>
                          <div class="d-flex align-items-center">
                            <i class="bi bi-folder2"></i> <span class="ps-2">{{ post.category }}</span>
                          </div>
                        </div>
                        <p>{{ post.excerpt }}</p>
                        <hr>
                        <a href="{% url 'blog_detail' post.slug %}" class="readmore stretched-link"><span>Read More</span><i class="bi bi-arrow-right"></i></a>
                      </div>
                    </article>
                  </div><!-- End post list item -->
                {% endfor %}
              {% else %}
                <div class="col-12 text-center py-5">
                  <h3>No blog posts available at the moment</h3>
                  <p>Please check back later for updates on our blog.</p>
                </div>
              {% endif %}
            </div>
            
            {% if blog_posts.count > 6 %}
            <!-- Blog Pagination Section -->
            <div id="blog-pagination" class="blog-pagination mt-5">
              <div class="d-flex justify-content-center">
                <ul>
                  <li><a href="#"><i class="bi bi-chevron-left"></i></a></li>
                  <li><a href="#" class="active">1</a></li>
                  <li><a href="#">2</a></li>
                  <li><a href="#">3</a></li>
                  <li><a href="#"><i class="bi bi-chevron-right"></i></a></li>
                </ul>
              </div>
            </div><!-- /Blog Pagination Section -->
            {% endif %}
          </div>
          
          <!-- Sidebar Column -->
          <div class="col-lg-4 sidebar">
            <div class="widgets-container">
              <!-- Search Widget -->
              <div class="search-widget widget-item">
                <h3 class="widget-title">Search</h3>
                <form action="">
                  <input type="text" placeholder="Search blog posts...">
                  <button type="submit" title="Search"><i class="bi bi-search"></i></button>
                </form>
              </div><!--/Search Widget -->

              <!-- Categories Widget -->
              {% if blog_categories %}
              <div class="categories-widget widget-item">
                <h3 class="widget-title">Categories</h3>
                <ul class="mt-3">
                  {% for category in blog_categories %}
                  <li><a href="#">{{ category }} 
                    <span>(
                    {% with count=0 %}
                      {% for post in blog_posts %}
                        {% if post.category == category %}
                          {% with count=count|add:1 %}{% endwith %}
                        {% endif %}
                      {% endfor %}
                      {{ count }}
                    {% endwith %}
                    )</span>
                  </a></li>
                  {% endfor %}
                </ul>
              </div><!--/Categories Widget -->
              {% endif %}

              <!-- Recent Posts Widget -->
              {% if recent_posts %}
              <div class="recent-posts-widget widget-item">
                <h3 class="widget-title">Recent Posts</h3>
                {% for post in recent_posts %}
                <div class="post-item">
                  {% if post.image %}
                  <img src="{{ post.image.url }}" alt="{{ post.title }}" class="flex-shrink-0">
                  {% endif %}
                  <div>
                    <h4><a href="{% url 'blog_detail' post.slug %}">{{ post.title }}</a></h4>
                    <time datetime="{{ post.created_at|date:'Y-m-d' }}">{{ post.created_at|date:"M d, Y" }}</time>
                  </div>
                </div><!-- End recent post item-->
                {% endfor %}
              </div><!--/Recent Posts Widget -->
              {% endif %}
            </div>
          </div>
          <!-- End Sidebar Column -->
        </div>
      </div>
    </section><!-- /Blog Posts Section -->
{% endblock %}