{% extends "base.html" %}

{% block content %}
    <!-- Page Title -->
    <div class="page-title dark-background" style="background-image: url(assets/img/page-title-bg.jpg);">
      <div class="container position-relative">
        <h1>{{ project.title }}</h1>
        <nav class="breadcrumbs">
          <ol>
            <li><a href="{% url 'index' %}">Home</a></li>
            <li><a href="{% url 'projects' %}">Projects</a></li>
            <li class="current">{{ project.title }}</li>
          </ol>
        </nav>
      </div>
    </div><!-- End Page Title -->

    <!-- Project Details Section -->
    <section id="project-details" class="project-details section">
      <div class="container" data-aos="fade-up">
        <div class="row justify-content-between gy-4">
          <div class="col-lg-8" data-aos="fade-up">
            {% if project.image %}
            <div class="project-image mb-4">
              <img src="{{ project.image.url }}" alt="{{ project.title }}" class="img-fluid">
            </div>
            {% endif %}
            
            <div class="portfolio-description">
              <h2>{{ project.title }}</h2>
              <div class="project-content">
                {{ project.description|safe }}
              </div>
            </div>
          </div>

          <div class="col-lg-3" data-aos="fade-up" data-aos-delay="100">
            <div class="portfolio-info">
              <h3>Project information</h3>
              <ul>
                <li><strong>Category</strong> {{ project.category }}</li>
                <li><strong>Client</strong> {{ project.client }}</li>
                <li><strong>Project date</strong> {{ project.project_date|date:"d F, Y" }}</li>
                {% if project.project_url %}
                <li><strong>Project URL</strong> <a href="{{ project.project_url }}" target="_blank">{{ project.project_url }}</a></li>
                <li><a href="{{ project.project_url }}" class="btn-visit align-self-start" target="_blank">Visit Website</a></li>
                {% endif %}
              </ul>
            </div>
            
            {% if site_settings %}
            <div class="contact-info mt-4">
              <h3>Need a similar project?</h3>
              <p>Contact us to discuss your requirements</p>
              <ul class="mt-3">
                <li><i class="bi bi-envelope me-2"></i> {{ site_settings.email }}</li>
                <li><i class="bi bi-phone me-2"></i> {{ site_settings.phone }}</li>
              </ul>
              <a href="{% url 'contact' %}" class="btn btn-primary mt-3">Contact Us</a>
            </div>
            {% endif %}
          </div>
        </div>
        
        {% if related_projects %}
        <!-- Related Projects -->
        <div class="related-projects mt-5">
          <h3 class="mb-4">Related Projects</h3>
          <div class="row gy-4">
            {% for related in related_projects %}
            <div class="col-lg-4 col-md-6">
              <div class="portfolio-content h-100">
                {% if related.image %}
                <img src="{{ related.image.url }}" class="img-fluid" alt="{{ related.title }}">
                {% endif %}
                <div class="portfolio-info">
                  <h4>{{ related.title }}</h4>
                  <p>{{ related.description|truncatewords:10 }}</p>
                  {% if related.image %}
                  <a href="{{ related.image.url }}" title="{{ related.title }}" data-gallery="portfolio-gallery-related" class="glightbox preview-link"><i class="bi bi-zoom-in"></i></a>
                  {% endif %}
                  <a href="{% url 'project_detail' related.slug %}" title="More Details" class="details-link"><i class="bi bi-link-45deg"></i></a>
                </div>
              </div>
            </div>
            {% endfor %}
          </div>
        </div>
        {% endif %}
      </div>
    </section><!-- /Project Details Section -->
{% endblock %}