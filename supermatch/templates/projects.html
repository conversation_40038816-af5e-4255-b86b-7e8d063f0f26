{% extends "base.html" %}

{% block content %}




    <!-- Page Title -->
    <div class="page-title dark-background" style="background-image: url(assets/img/page-title-bg.jpg);">
      <div class="container position-relative">
        <h1>Projects</h1>
        <nav class="breadcrumbs">
          <ol>
            <li><a href="{% url 'index' %}">Home</a></li>
            <li class="current">Projects</li>
          </ol>
        </nav>
      </div>
    </div><!-- End Page Title -->

    <!-- Projects Section -->
    <section id="projects" class="projects section">
      <div class="container">
        {% if projects %}
        <div class="isotope-layout" data-default-filter="*" data-layout="masonry" data-sort="original-order">
          <ul class="portfolio-filters isotope-filters" data-aos="fade-up" data-aos-delay="100">
            <li data-filter="*" class="filter-active">All</li>
            {% for category in project_categories %}
            <li data-filter=".filter-{{ category|lower }}">{{ category }}</li>
            {% endfor %}
          </ul><!-- End Portfolio Filters -->

          <div class="row gy-4 isotope-container" data-aos="fade-up" data-aos-delay="200">
            {% for project in projects %}
            <div class="col-lg-4 col-md-6 portfolio-item isotope-item filter-{{ project.category|lower }}">
              <div class="portfolio-content h-100">
                {% if project.image %}
                <img src="{{ project.image.url }}" class="img-fluid" alt="{{ project.title }}">
                {% endif %}
                <div class="portfolio-info">
                  <h4>{{ project.title }}</h4>
                  <p>{{ project.description|truncatewords:10 }}</p>
                  {% if project.image %}
                  <a href="{{ project.image.url }}" title="{{ project.title }}" data-gallery="portfolio-gallery-{{ project.category|lower }}" class="glightbox preview-link"><i class="bi bi-zoom-in"></i></a>
                  {% endif %}
                  <a href="{% url 'project_detail' project.slug %}" title="More Details" class="details-link"><i class="bi bi-link-45deg"></i></a>
                </div>
              </div>
            </div><!-- End Portfolio Item -->
            {% endfor %}
          </div><!-- End Portfolio Container -->
        </div>
        {% else %}
        <div class="row">
          <div class="col-12 text-center py-5">
            <h3>No projects available at the moment</h3>
            <p>Please check back later for updates on our latest projects.</p>
          </div>
        </div>
        {% endif %}
      </div>
    </section><!-- /Projects Section -->



{% endblock  %}