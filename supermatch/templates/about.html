{% extends 'base.html' %}

{% block content %}
  <!-- Page Title -->
  <div class="page-title dark-background" style="background-image: url(assets/img/page-title-bg.jpg);">
    <div class="container position-relative">
      <h1>About</h1>
      <nav class="breadcrumbs">
        <ol>
          <li>
            <a href="{% url 'index' %}">Home</a>
          </li>
          <li class="current">About</li>
        </ol>
      </nav>
    </div>
  </div>
  <!-- End Page Title -->

  <!-- About Section -->
  {% if about_section %}
  <section id="about" class="about section">
    <div class="container">
      <div class="row position-relative">
        <div class="col-lg-7 about-img" data-aos="zoom-out" data-aos-delay="200">
          <img src="{{ about_section.image.url }}" alt="{{ about_section.title }}" />
        </div>

        <div class="col-lg-7" data-aos="fade-up" data-aos-delay="100">
          <h2 class="inner-title">{{ about_section.title }}</h2>
          <div class="our-story">
            <h4>Est {{ about_section.year_established }}</h4>
            <h3>{{ about_section.subtitle }}</h3>
            <p>{{ about_section.description|safe }}</p>
            
            {% if about_section.video_url %}
            <div class="watch-video d-flex align-items-center position-relative">
              <i class="bi bi-play-circle"></i>
              <a href="{{ about_section.video_url }}" class="glightbox stretched-link">Watch Video</a>
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </section>
  {% endif %}
  <!-- /About Section -->

  <!-- Mission & Vision Section -->
  {% if about_section %}
  <section id="alt-services" class="alt-services section">
    <div class="container">
      <div class="row justify-content-around gy-4">
        <div class="features-image col-lg-6" data-aos="fade-up" data-aos-delay="100">
          <img src="assets/img/alt-services.jpg" alt="" />
        </div>

        <div class="col-lg-5 d-flex flex-column justify-content-center" data-aos="fade-up" data-aos-delay="200">
          {% if about_section.mission_title and about_section.mission_description %}
          <div class="icon-box d-flex position-relative" data-aos="fade-up" data-aos-delay="300">
            <i class="bi bi-flag flex-shrink-0"></i>
            <div>
              <h4>{{ about_section.mission_title }}</h4>
              <p>{{ about_section.mission_description|safe }}</p>
            </div>
          </div>
          {% endif %}
          
          {% if about_section.vision_title and about_section.vision_description %}
          <div class="icon-box d-flex position-relative" data-aos="fade-up" data-aos-delay="400">
            <i class="bi bi-eye flex-shrink-0"></i>
            <div>
              <h4>{{ about_section.vision_title }}</h4>
              <p>{{ about_section.vision_description|safe }}</p>
            </div>
          </div>
          {% endif %}
          
          {% if about_section.values_title and about_section.values_description %}
          <div class="icon-box d-flex position-relative" data-aos="fade-up" data-aos-delay="500">
            <i class="bi bi-heart flex-shrink-0"></i>
            <div>
              <h4>{{ about_section.values_title }}</h4>
              <p>{{ about_section.values_description|safe }}</p>
            </div>
          </div>
          {% endif %}
        </div>
      </div>
    </div>
  </section>
  {% endif %}
  <!-- /Mission & Vision Section -->

  <!-- Team Section -->
  {% if team_members %}
  <section id="team" class="team section">
    <!-- Section Title -->
    <div class="container section-title" data-aos="fade-up">
      <h2>Our Team</h2>
      <p>Meet the talented individuals behind our success</p>
    </div>
    <!-- End Section Title -->

    <div class="container">
      <div class="row gy-5">
        {% for member in team_members %}
        <div class="col-lg-4 col-md-6 member" data-aos="fade-up" data-aos-delay="{{ forloop.counter|add:"100" }}">
          <div class="member-img">
            <img src="{{ member.image.url }}" class="img-fluid" alt="{{ member.name }}" />
            <div class="social">
              {% if member.twitter_url %}
              <a href="{{ member.twitter_url }}"><i class="bi bi-twitter-x"></i></a>
              {% endif %}
              {% if member.facebook_url %}
              <a href="{{ member.facebook_url }}"><i class="bi bi-facebook"></i></a>
              {% endif %}
              {% if member.instagram_url %}
              <a href="{{ member.instagram_url }}"><i class="bi bi-instagram"></i></a>
              {% endif %}
              {% if member.linkedin_url %}
              <a href="{{ member.linkedin_url }}"><i class="bi bi-linkedin"></i></a>
              {% endif %}
            </div>
          </div>
          <div class="member-info text-center">
            <h4>{{ member.name }}</h4>
            <span>{{ member.position }}</span>
            <p>{{ member.bio }}</p>
          </div>
        </div>
        {% endfor %}
        <!-- End Team Member -->
      </div>
    </div>
  </section>
  {% endif %}
  <!-- /Team Section -->
{% endblock %}
