from django.shortcuts import render, get_object_or_404
from django.http import HttpResponseRedirect
from django.contrib import messages
from django.conf import settings
from .models import Service, Project, BlogPost, Contact, SiteSettings, TeamMember, AboutSection

def get_site_settings():
    return SiteSettings.objects.first()

def index(request):
    # Get site settings
    site_settings = get_site_settings()
    
    # Get services
    services = Service.objects.all()[:6]  # Get latest 6 services
    
    # Get projects and their categories
    projects = Project.objects.all()[:9]  # Get latest 9 projects
    project_categories = Project.objects.values_list('category', flat=True).distinct()
    
    # Get recent blog posts
    recent_posts = BlogPost.objects.all()[:3]  # Get latest 3 blog posts
        
    # Get team members
    team_members = TeamMember.objects.all()
    
    # Get about section
    about_section = AboutSection.objects.first()
    
    # Get testimonials (if you have a Testimonial model)
    testimonials = []  # You'll need to create a Testimonial model if you want to make this dynamic
    
    context = {
        'site_settings': site_settings,
        'services': services,
        'projects': projects,
        'project_categories': project_categories,
        'recent_posts': recent_posts,
        'team_members': team_members,
        'about_section': about_section,
        'testimonials': testimonials,
        'body_class': 'index-page',
    }
    return render(request, 'index.html', context)

def about(request):
    # Get site settings
    site_settings = get_site_settings()
    
    # Get about section data
    about_section = AboutSection.objects.first()
    
    # Get team members
    team_members = TeamMember.objects.all()
    
    context = {
        'site_settings': site_settings,
        'about_section': about_section,
        'team_members': team_members,
        'body_class': 'about-page',
    }
    return render(request, 'about.html', context)

def services(request):
    # Get all services
    services = Service.objects.all()
    
    # Get site settings
    site_settings = get_site_settings()
    
    # Get about section for additional info if needed
    about_section = AboutSection.objects.first()
    
    context = {
        'services': services,
        'site_settings': site_settings,
        'about_section': about_section,
        'body_class': 'services-page',
    }
    return render(request, 'services.html', context)

def service_details(request, slug):
    # Get the specific service
    service = get_object_or_404(Service, slug=slug)
    
    # Get all services for the sidebar
    all_services = Service.objects.all()
    
    # Get site settings
    site_settings = get_site_settings()
    
    context = {
        'service': service,
        'all_services': all_services,
        'site_settings': site_settings,
        'body_class': 'service-details-page',
    }
    return render(request, 'service-details.html', context)

def projects(request):
    # Get all projects
    projects = Project.objects.all()
    
    # Get distinct project categories
    project_categories = Project.objects.values_list('category', flat=True).distinct()
    
    # Get site settings
    site_settings = get_site_settings()
    
    context = {
        'projects': projects,
        'project_categories': project_categories,
        'site_settings': site_settings,
        'body_class': 'projects-page',
    }
    return render(request, 'projects.html', context)

def project_details(request, slug):
    # Get the specific project
    project = get_object_or_404(Project, slug=slug)
    
    # Get related projects in the same category (excluding current project)
    related_projects = Project.objects.filter(category=project.category).exclude(id=project.id)[:3]
    
    # Get site settings
    site_settings = get_site_settings()
    
    context = {
        'project': project,
        'related_projects': related_projects,
        'site_settings': site_settings,
        'body_class': 'project-details-page',
    }
    return render(request, 'project-details.html', context)

def blog(request):
    # Get all blog posts
    blog_posts = BlogPost.objects.all()
    
    # Get distinct blog categories
    blog_categories = BlogPost.objects.values_list('category', flat=True).distinct()
    
    # Get recent posts for sidebar
    recent_posts = BlogPost.objects.all().order_by('-created_at')[:5]
    
    # Get site settings
    site_settings = get_site_settings()
    
    context = {
        'blog_posts': blog_posts,
        'blog_categories': blog_categories,
        'recent_posts': recent_posts,
        'site_settings': site_settings,
        'body_class': 'blog-page',
    }
    return render(request, 'blog.html', context)

def blog_details(request, slug):
    # Get the specific blog post
    blog_post = get_object_or_404(BlogPost, slug=slug)
    
    # Get distinct blog categories
    blog_categories = BlogPost.objects.values_list('category', flat=True).distinct()
    
    # Get recent posts for sidebar
    recent_posts = BlogPost.objects.exclude(id=blog_post.id).order_by('-created_at')[:5]
    
    # Convert tags string to list
    if blog_post.tags:
        tags = [tag.strip() for tag in blog_post.tags.split(',')]
    else:
        tags = []
    
    # Get site settings
    site_settings = get_site_settings()
    
    context = {
        'blog_post': blog_post,
        'blog_categories': blog_categories,
        'recent_posts': recent_posts,
        'tags': tags,
        'site_settings': site_settings,
        'body_class': 'blog-details-page',
    }
    return render(request, 'blog-details.html', context)

def contact(request):
    # Get site settings
    site_settings = get_site_settings()
    
    # Get recent blog posts for potential sidebar
    recent_posts = BlogPost.objects.all().order_by('-created_at')[:3]
    
    if request.method == 'POST':
        name = request.POST.get('name')
        email = request.POST.get('email')
        subject = request.POST.get('subject')
        message = request.POST.get('message')
        
        # Validate form data
        if name and email and subject and message:
            # Create new contact entry
            Contact.objects.create(
                name=name,
                email=email,
                subject=subject,
                message=message
            )
            
            messages.success(request, 'Your message has been sent successfully! We will get back to you soon.')
            return HttpResponseRedirect('/contact/')
        else:
            messages.error(request, 'Please fill in all required fields.')
    
    context = {
        'site_settings': site_settings,
        'recent_posts': recent_posts,
        'body_class': 'contact-page',
        'page_title': 'Contact Us',
    }
    return render(request, 'contact.html', context)
