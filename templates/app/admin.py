from django.contrib import admin
from .models import AboutSection, BlogPost, Contact, Project, Service, SiteSettings, TeamMember

@admin.register(Service)
class ServiceAdmin(admin.ModelAdmin):
    list_display = ('title', 'icon', 'created_at', 'updated_at')
    list_filter = ('created_at', 'updated_at')
    search_fields = ('title', 'description')
    prepopulated_fields = {'slug': ('title',)}
    readonly_fields = ('created_at', 'updated_at')

@admin.register(Project)
class ProjectAdmin(admin.ModelAdmin):
    list_display = ('title', 'category', 'client', 'project_date', 'created_at')
    list_filter = ('category', 'project_date', 'created_at')
    search_fields = ('title', 'description', 'client')
    prepopulated_fields = {'slug': ('title',)}
    readonly_fields = ('created_at', 'updated_at')
    date_hierarchy = 'project_date'

@admin.register(BlogPost)
class BlogPostAdmin(admin.ModelAdmin):
    list_display = ('title', 'author', 'category', 'created_at')
    list_filter = ('category', 'author', 'created_at')
    search_fields = ('title', 'content', 'author')
    prepopulated_fields = {'slug': ('title',)}
    readonly_fields = ('created_at', 'updated_at')

@admin.register(Contact)
class ContactAdmin(admin.ModelAdmin):
    list_display = ('name', 'email', 'subject', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('name', 'email', 'subject', 'message')
    readonly_fields = ('created_at',)
    ordering = ('-created_at',)

@admin.register(SiteSettings)
class SiteSettingsAdmin(admin.ModelAdmin):
    list_display = ('site_name', 'email', 'phone', 'updated_at')
    readonly_fields = ('updated_at',)
    fieldsets = (
        ('Basic Information', {
            'fields': ('site_name', 'site_description')
        }),
        ('Contact Information', {
            'fields': ('email', 'phone', 'address')
        }),
        ('Social Media Links', {
            'fields': ('facebook_url', 'twitter_url', 'instagram_url', 'linkedin_url')
        }),
    )

    def has_add_permission(self, request):
        # Prevent creating multiple site settings instances
        return SiteSettings.objects.count() == 0

@admin.register(TeamMember)
class TeamMemberAdmin(admin.ModelAdmin):
    list_display = ('name', 'position', 'created_at', 'updated_at')
    list_filter = ('position', 'created_at')
    search_fields = ('name', 'position', 'bio')
    readonly_fields = ('created_at', 'updated_at')

@admin.register(AboutSection)
class AboutSectionAdmin(admin.ModelAdmin):
    list_display = ('title', 'subtitle', 'year_established')
    search_fields = ('title', 'subtitle', 'description')
    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'subtitle', 'description', 'year_established', 'image', 'video_url')
        }),
        ('Mission and Vision', {
            'fields': ('mission_title', 'mission_description', 'vision_title', 'vision_description', 
                      'values_title', 'values_description')
        }),
    )
    
    def has_add_permission(self, request):
        # Prevent creating multiple about section instances
        return AboutSection.objects.count() == 0
