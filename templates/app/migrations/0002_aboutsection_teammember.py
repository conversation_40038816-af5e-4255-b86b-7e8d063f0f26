# Generated by Django 5.1.7 on 2025-03-23 10:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AboutSection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('subtitle', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('year_established', models.CharField(max_length=20)),
                ('image', models.ImageField(upload_to='about/')),
                ('video_url', models.URLField(blank=True)),
                ('mission_title', models.CharField(default='Our Mission', max_length=200)),
                ('mission_description', models.TextField(blank=True)),
                ('vision_title', models.Char<PERSON>ield(default='Our Vision', max_length=200)),
                ('vision_description', models.TextField(blank=True)),
                ('values_title', models.CharField(default='Our Values', max_length=200)),
                ('values_description', models.TextField(blank=True)),
            ],
            options={
                'verbose_name': 'About Section',
                'verbose_name_plural': 'About Section',
            },
        ),
        migrations.CreateModel(
            name='TeamMember',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('position', models.CharField(max_length=100)),
                ('bio', models.TextField()),
                ('image', models.ImageField(upload_to='team/')),
                ('twitter_url', models.URLField(blank=True)),
                ('facebook_url', models.URLField(blank=True)),
                ('instagram_url', models.URLField(blank=True)),
                ('linkedin_url', models.URLField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
