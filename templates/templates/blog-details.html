{% extends "base.html" %}

{% block content %}

    <!-- Page Title -->
    <div class="page-title dark-background" style="background-image: url(assets/img/page-title-bg.jpg);">
      <div class="container position-relative">
        <h1>{{ blog_post.title }}</h1>
        <nav class="breadcrumbs">
          <ol>
            <li><a href="{% url 'index' %}">Home</a></li>
            <li><a href="{% url 'blog' %}">Blog</a></li>
            <li class="current">{{ blog_post.title }}</li>
          </ol>
        </nav>
      </div>
    </div><!-- End Page Title -->

    <div class="container">
      <div class="row">

        <div class="col-lg-8">

          <!-- Blog Details Section -->
          <section id="blog-details" class="blog-details section">
            <div class="container">

              <article class="article">

                {% if blog_post.image %}
                <div class="post-img">
                  <img src="{{ blog_post.image.url }}" alt="{{ blog_post.title }}" class="img-fluid">
                </div>
                {% endif %}

                <h2 class="title">{{ blog_post.title }}</h2>

                <div class="meta-top">
                  <ul>
                    <li class="d-flex align-items-center"><i class="bi bi-person"></i> <span>{{ blog_post.author }}</span></li>
                    <li class="d-flex align-items-center"><i class="bi bi-clock"></i> <time datetime="{{ blog_post.created_at|date:'Y-m-d' }}">{{ blog_post.created_at|date:"M d, Y" }}</time></li>
                    <li class="d-flex align-items-center"><i class="bi bi-folder2"></i> <span>{{ blog_post.category }}</span></li>
                  </ul>
                </div><!-- End meta top -->

                <div class="content">
                  {{ blog_post.content|safe }}
                </div><!-- End post content -->

                {% if tags %}
                <div class="meta-bottom">
                  <i class="bi bi-tags"></i>
                  <ul class="tags">
                    {% for tag in tags %}
                    <li><a href="#">{{ tag }}</a></li>
                    {% endfor %}
                  </ul>
                </div><!-- End meta bottom -->
                {% endif %}

              </article>

            </div>
          </section><!-- /Blog Details Section -->

          {% if site_settings %}
          <!-- Share Post Section -->
          <section id="blog-share" class="blog-share section">
            <div class="container">
              <div class="share-container">
                <h4>Share This Post</h4>
                <div class="social-links">
                  {% if site_settings.facebook_url %}
                  <a href="{{ site_settings.facebook_url }}" target="_blank"><i class="bi bi-facebook"></i></a>
                  {% endif %}
                  {% if site_settings.twitter_url %}
                  <a href="{{ site_settings.twitter_url }}" target="_blank"><i class="bi bi-twitter-x"></i></a>
                  {% endif %}
                  {% if site_settings.linkedin_url %}
                  <a href="{{ site_settings.linkedin_url }}" target="_blank"><i class="bi bi-linkedin"></i></a>
                  {% endif %}
                </div>
              </div>
            </div>
          </section><!-- /Share Post Section -->
          {% endif %}

        </div>

        <div class="col-lg-4 sidebar">

          <div class="widgets-container">

            <!-- Search Widget -->
            <div class="search-widget widget-item">

              <h3 class="widget-title">Search</h3>
              <form action="">
                <input type="text" placeholder="Search blog posts...">
                <button type="submit" title="Search"><i class="bi bi-search"></i></button>
              </form>

            </div><!--/Search Widget -->

            <!-- Categories Widget -->
            {% if blog_categories %}
            <div class="categories-widget widget-item">

              <h3 class="widget-title">Categories</h3>
              <ul class="mt-3">
                {% for category in blog_categories %}
                <li><a href="#">{{ category }}</a></li>
                {% endfor %}
              </ul>

            </div><!--/Categories Widget -->
            {% endif %}

            <!-- Recent Posts Widget -->
            {% if recent_posts %}
            <div class="recent-posts-widget widget-item">

              <h3 class="widget-title">Recent Posts</h3>

              {% for post in recent_posts %}
              <div class="post-item">
                {% if post.image %}
                <img src="{{ post.image.url }}" alt="{{ post.title }}" class="flex-shrink-0">
                {% endif %}
                <div>
                  <h4><a href="{% url 'blog_detail' post.slug %}">{{ post.title }}</a></h4>
                  <time datetime="{{ post.created_at|date:'Y-m-d' }}">{{ post.created_at|date:"M d, Y" }}</time>
                </div>
              </div><!-- End recent post item-->
              {% endfor %}

            </div><!--/Recent Posts Widget -->
            {% endif %}

            <!-- Tags Widget -->
            {% if tags %}
            <div class="tags-widget widget-item">

              <h3 class="widget-title">Tags</h3>
              <ul>
                {% for tag in tags %}
                <li><a href="#">{{ tag }}</a></li>
                {% endfor %}
              </ul>

            </div><!--/Tags Widget -->
            {% endif %}

          </div>

        </div>

      </div>
    </div>

{% endblock  %}
