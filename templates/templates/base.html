{% load static %}
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta content="width=device-width, initial-scale=1.0" name="viewport">
  <title>{% block title %}{{ site_settings.site_name }}{% endblock %}</title>
  <meta name="description" content="{{ site_settings.site_description }}">
  <meta name="keywords" content="{{ site_settings.site_keywords }}">

  <!-- Favicons -->
  {% comment %} <link href="{% static 'assets/img/favicon.png' %}" rel="icon"> {% endcomment %}
  <link href="{% static 'assets/img/apple-touch-icon.png' %}" rel="apple-touch-icon">

  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- Font Awesome for icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Vendor CSS Files -->
  <link href="{% static 'assets/vendor/bootstrap/css/bootstrap.min.css' %}" rel="stylesheet">
  <link href="{% static 'assets/vendor/bootstrap-icons/bootstrap-icons.css' %}" rel="stylesheet">
  <link href="{% static 'assets/vendor/aos/aos.css' %}" rel="stylesheet">
  <link href="{% static 'assets/vendor/fontawesome-free/css/all.min.css' %}" rel="stylesheet">
  <link href="{% static 'assets/vendor/glightbox/css/glightbox.min.css' %}" rel="stylesheet">
  <link href="{% static 'assets/vendor/swiper/swiper-bundle.min.css' %}" rel="stylesheet">

  <!-- Main CSS File -->
  <link href="{% static 'assets/css/main.css' %}" rel="stylesheet">

  <!-- Custom CSS with theme variables -->
  <style>
    :root {
        /* Primary Colors */
        --primary-50: #eff6ff;
        --primary-100: #dbeafe;
        --primary-200: #bfdbfe;
        --primary-300: #93c5fd;
        --primary-400: #60a5fa;
        --primary-500: #3b82f6;
        --primary-600: #2563eb;
        --primary-700: #1d4ed8;
        --primary-800: #1e40af;
        --primary-900: #1e3a8a;

        /* Secondary Colors */
        --secondary-50: #f0f9ff;
        --secondary-100: #e0f2fe;
        --secondary-200: #bae6fd;
        --secondary-300: #7dd3fc;
        --secondary-400: #38bdf8;
        --secondary-500: #0ea5e9;
        --secondary-600: #0284c7;
        --secondary-700: #0369a1;
        --secondary-800: #075985;
        --secondary-900: #0c4a6e;

        /* Accent Colors */
        --accent-50: #ecfdf5;
        --accent-100: #d1fae5;
        --accent-200: #a7f3d0;
        --accent-300: #6ee7b7;
        --accent-400: #34d399;
        --accent-500: #10b981;
        --accent-600: #059669;
        --accent-700: #047857;
        --accent-800: #065f46;
        --accent-900: #064e3b;

        /* Gradients */
        --gradient-primary: linear-gradient(135deg, var(--primary-500) 0%, var(--secondary-500) 100%);
        --gradient-secondary: linear-gradient(135deg, var(--secondary-400) 0%, var(--primary-400) 100%);
        --gradient-accent: linear-gradient(135deg, var(--accent-400) 0%, var(--primary-400) 100%);

        /* Shadows */
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    }

    /* Custom utility classes */
    .gradient-primary { background: var(--gradient-primary); }
    .gradient-secondary { background: var(--gradient-secondary); }
    .gradient-accent { background: var(--gradient-accent); }

    .text-primary-500 { color: var(--primary-500); }
    .text-secondary-500 { color: var(--secondary-500); }
    .text-accent-500 { color: var(--accent-500); }

    .bg-primary-50 { background-color: var(--primary-50); }
    .bg-primary-500 { background-color: var(--primary-500); }
    .bg-secondary-500 { background-color: var(--secondary-500); }

    /* Smooth scrolling */
    html { scroll-behavior: smooth; }

    /* Custom animations */
    .fade-in { animation: fadeIn 0.6s ease-in; }
    .slide-up { animation: slideUp 0.6s ease-out; }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    @keyframes slideUp {
        from { transform: translateY(30px); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
    }

    /* Navigation hover effects */
    .nav-link {
        position: relative;
        transition: all 0.3s ease;
    }

    .nav-link::after {
        content: '';
        position: absolute;
        width: 0;
        height: 2px;
        bottom: -4px;
        left: 50%;
        background: var(--gradient-primary);
        transition: all 0.3s ease;
        transform: translateX(-50%);
    }

    .nav-link:hover::after,
    .nav-link.active::after {
        width: 100%;
    }

    /* Card hover effects */
    .card-hover {
        transition: all 0.3s ease;
    }

    .card-hover:hover {
        transform: translateY(-8px);
        box-shadow: var(--shadow-xl);
    }

    /* Partnership slider styles */
    .partnership-swiper {
        padding: 20px 0;
    }

    .partner-logo {
        height: 120px;
        transition: all 0.3s ease;
    }

    .partner-logo:hover {
        transform: scale(1.05);
    }

    .logo-placeholder {
        width: 150px;
        height: 100px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }

    .logo-placeholder:hover {
        border-color: var(--primary-500);
        background-color: var(--primary-50) !important;
    }

    /* Enhanced Hero Section Styles */
    .hero {
        min-height: 100vh;
        position: relative;
        display: flex;
        align-items: center;
    }

    .hero .carousel-item {
        height: 100vh;
    }

    .hero .carousel-item img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .hero .carousel-control-prev,
    .hero .carousel-control-next {
        width: 5%;
        opacity: 0.8;
        transition: opacity 0.3s ease;
    }

    .hero .carousel-control-prev:hover,
    .hero .carousel-control-next:hover {
        opacity: 1;
    }

    .hero .carousel-control-prev-icon,
    .hero .carousel-control-next-icon {
        width: 3rem;
        height: 3rem;
        background-size: 100%, 100%;
    }

    .hero .carousel-indicators {
        bottom: 2rem;
    }

    .hero .carousel-indicators button {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin: 0 5px;
        background-color: rgba(255, 255, 255, 0.5);
        border: 2px solid rgba(255, 255, 255, 0.8);
        transition: all 0.3s ease;
    }

    .hero .carousel-indicators button.active {
        background-color: white;
        transform: scale(1.2);
    }

    /* Hero content animations */
    @keyframes heroFadeIn {
        from {
            opacity: 0;
            transform: translateY(50px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .hero .slide-up {
        animation: heroFadeIn 1s ease-out forwards;
    }

    .hero .slide-up:nth-child(2) {
        animation-delay: 0.2s;
    }

    .hero .slide-up:nth-child(3) {
        animation-delay: 0.4s;
    }
  </style>

  {% block extra_css %}{% endblock %}
</head>

<body class="{{ body_class }}">

  <!-- Navigation -->
  <nav class="bg-white shadow-lg sticky top-0 z-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
            <!-- Logo -->
            <div class="flex items-center">
                <a href="{% url 'index' %}" class="flex items-center space-x-3">
                    <div class="w-10 h-10 gradient-primary rounded-lg flex items-center justify-center">
                        {% if site_settings.logo %}
                            <img src="{{ site_settings.logo.url }}" alt="{{ site_settings.site_name }}" class="w-full h-full object-cover rounded-lg">
                        {% else %}
                            <i class="fas fa-building text-white text-lg"></i>
                        {% endif %}
                    </div>
                    <div>
                        <div class="text-xl font-bold text-gray-900">{{ site_settings.site_name }}</div>
                        <div class="text-xs text-gray-600 hidden sm:block">{{ site_settings.site_description|truncatewords:3 }}</div>
                    </div>
                </a>
            </div>

            <!-- Desktop Navigation -->
            <div class="hidden md:flex items-center space-x-4">
                <a href="{% url 'index' %}" class="nav-link {% if request.path == '/' %}active{% endif %} text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium">Home</a>
                <a href="{% url 'about' %}" class="nav-link {% if 'about' in request.path %}active{% endif %} text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium">About</a>
                <a href="{% url 'services' %}" class="nav-link {% if 'services' in request.path %}active{% endif %} text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium">Services</a>
                <a href="{% url 'projects' %}" class="nav-link {% if 'projects' in request.path %}active{% endif %} text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium">Projects</a>
                <a href="{% url 'blog' %}" class="nav-link {% if 'blog' in request.path %}active{% endif %} text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium">Blog</a>
                <a href="{% url 'contact' %}" class="nav-link {% if 'contact' in request.path %}active{% endif %} text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium">Contact</a>

                <!-- Get Quote Button -->
                <a href="{% url 'contact' %}" class="gradient-primary text-white px-6 py-2 rounded-full text-sm font-medium hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                    <i class="fas fa-quote-left mr-2"></i>Get Quote
                </a>
            </div>

            <!-- Mobile menu button -->
            <div class="md:hidden">
                <button type="button" class="mobile-menu-button text-gray-500 hover:text-gray-600 focus:outline-none focus:text-gray-600" aria-label="toggle menu">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Mobile Navigation -->
    <div class="mobile-menu hidden md:hidden bg-white border-t border-gray-200">
        <div class="px-2 pt-2 pb-3 space-y-1">
            <a href="{% url 'index' %}" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600 hover:bg-gray-50">Home</a>
            <a href="{% url 'about' %}" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600 hover:bg-gray-50">About</a>
            <a href="{% url 'services' %}" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600 hover:bg-gray-50">Services</a>
            <a href="{% url 'projects' %}" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600 hover:bg-gray-50">Projects</a>
            <a href="{% url 'blog' %}" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600 hover:bg-gray-50">Blog</a>
            <a href="{% url 'contact' %}" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600 hover:bg-gray-50">Contact</a>
            <a href="{% url 'contact' %}" class="block mx-3 my-2 gradient-primary text-white px-4 py-2 rounded-lg text-center font-medium">
                <i class="fas fa-quote-left mr-2"></i>Get Quote
            </a>
        </div>
    </div>
  </nav>

  <main class="main">
    {% block content %}{% endblock %}
  </main>

  <!-- Footer -->
  <footer class="bg-gray-900 text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
            <!-- Organization Info -->
            <div class="md:col-span-2">
                <div class="flex items-center space-x-3 mb-4">
                    <div class="w-12 h-12 gradient-primary rounded-lg flex items-center justify-center">
                        {% if site_settings.logo %}
                            <img src="{{ site_settings.logo.url }}" alt="{{ site_settings.site_name }}" class="w-full h-full object-cover rounded-lg">
                        {% else %}
                            <i class="fas fa-building text-white text-xl"></i>
                        {% endif %}
                    </div>
                    <div>
                        <div class="text-2xl font-bold">{{ site_settings.site_name }}</div>
                        <div class="text-gray-400">{{ site_settings.site_description|truncatewords:5 }}</div>
                    </div>
                </div>
                <p class="text-gray-300 mb-4">{{ site_settings.site_description }}</p>
                <div class="flex space-x-4">
                    {% if site_settings.twitter_url %}<a href="{{ site_settings.twitter_url }}" class="text-gray-400 hover:text-white transition-colors"><i class="fab fa-twitter text-xl"></i></a>{% endif %}
                    {% if site_settings.facebook_url %}<a href="{{ site_settings.facebook_url }}" class="text-gray-400 hover:text-white transition-colors"><i class="fab fa-facebook text-xl"></i></a>{% endif %}
                    {% if site_settings.instagram_url %}<a href="{{ site_settings.instagram_url }}" class="text-gray-400 hover:text-white transition-colors"><i class="fab fa-instagram text-xl"></i></a>{% endif %}
                    {% if site_settings.linkedin_url %}<a href="{{ site_settings.linkedin_url }}" class="text-gray-400 hover:text-white transition-colors"><i class="fab fa-linkedin text-xl"></i></a>{% endif %}
                </div>
            </div>

            <!-- Quick Links -->
            <div>
                <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                <ul class="space-y-2">
                    <li><a href="{% url 'about' %}" class="text-gray-300 hover:text-white transition-colors">About Us</a></li>
                    <li><a href="{% url 'services' %}" class="text-gray-300 hover:text-white transition-colors">Services</a></li>
                    <li><a href="{% url 'projects' %}" class="text-gray-300 hover:text-white transition-colors">Projects</a></li>
                    <li><a href="{% url 'blog' %}" class="text-gray-300 hover:text-white transition-colors">Blog</a></li>
                    <li><a href="{% url 'contact' %}" class="text-gray-300 hover:text-white transition-colors">Contact</a></li>
                </ul>
            </div>

            <!-- Contact Info -->
            <div>
                <h3 class="text-lg font-semibold mb-4">Contact Info</h3>
                <div class="space-y-2 text-gray-300">
                    {% if site_settings.address %}
                    <div class="flex items-center">
                        <i class="fas fa-map-marker-alt w-4 mr-3"></i>
                        <span>{{ site_settings.address }}</span>
                    </div>
                    {% endif %}
                    {% if site_settings.phone %}
                    <div class="flex items-center">
                        <i class="fas fa-phone w-4 mr-3"></i>
                        <span>{{ site_settings.phone }}</span>
                    </div>
                    {% endif %}
                    {% if site_settings.email %}
                    <div class="flex items-center">
                        <i class="fas fa-envelope w-4 mr-3"></i>
                        <span>{{ site_settings.email }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 {{ site_settings.site_name }}. All rights reserved.</p>
            <p class="mt-2">Design & Developed💙 | Imagine Infotech| नेपालमा बनेको</p>
        </div>
    </div>
  </footer>

  <!-- Scroll Top -->
  <a href="#" id="scroll-top" class="scroll-top d-flex align-items-center justify-content-center"><i class="bi bi-arrow-up-short"></i></a>

  <!-- Preloader -->
  <div id="preloader"></div>

  <!-- Vendor JS Files -->
  <script src="{% static 'assets/vendor/bootstrap/js/bootstrap.bundle.min.js' %}"></script>
  <script src="{% static 'assets/vendor/php-email-form/validate.js' %}"></script>
  <script src="{% static 'assets/vendor/aos/aos.js' %}"></script>
  <script src="{% static 'assets/vendor/glightbox/js/glightbox.min.js' %}"></script>
  <script src="{% static 'assets/vendor/imagesloaded/imagesloaded.pkgd.min.js' %}"></script>
  <script src="{% static 'assets/vendor/isotope-layout/isotope.pkgd.min.js' %}"></script>
  <script src="{% static 'assets/vendor/swiper/swiper-bundle.min.js' %}"></script>
  <script src="{% static 'assets/vendor/purecounter/purecounter_vanilla.js' %}"></script>

  <!-- Main JS File -->
  <script src="{% static 'assets/js/main.js' %}"></script>

  <!-- JavaScript for mobile menu -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
        const mobileMenuButton = document.querySelector('.mobile-menu-button');
        const mobileMenu = document.querySelector('.mobile-menu');

        if (mobileMenuButton && mobileMenu) {
            mobileMenuButton.addEventListener('click', function() {
                mobileMenu.classList.toggle('hidden');
            });

            // Close mobile menu when clicking on a link
            const mobileMenuLinks = mobileMenu.querySelectorAll('a');
            mobileMenuLinks.forEach(link => {
                link.addEventListener('click', function() {
                    mobileMenu.classList.add('hidden');
                });
            });
        }
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Initialize partnership slider
    if (document.querySelector('.partnership-swiper')) {
        new Swiper('.partnership-swiper', {
            loop: true,
            autoplay: {
                delay: 3000,
                disableOnInteraction: false,
            },
            slidesPerView: 1,
            spaceBetween: 30,
            breakpoints: {
                640: {
                    slidesPerView: 2,
                },
                768: {
                    slidesPerView: 3,
                },
                1024: {
                    slidesPerView: 4,
                },
                1280: {
                    slidesPerView: 5,
                }
            }
        });
    }
  </script>

  {% block extra_js %}{% endblock %}

</body>

</html>