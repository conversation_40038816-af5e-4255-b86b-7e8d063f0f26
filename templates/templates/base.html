{% load static %}
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta content="width=device-width, initial-scale=1.0" name="viewport">
  <title>{% block title %}{{ site_settings.site_name }}{% endblock %}</title>
  <meta name="description" content="{{ site_settings.site_description }}">
  <meta name="keywords" content="{{ site_settings.site_keywords }}">

  <!-- Favicons -->
  {% comment %} <link href="{% static 'assets/img/favicon.png' %}" rel="icon"> {% endcomment %}
  <link href="{% static 'assets/img/apple-touch-icon.png' %}" rel="apple-touch-icon">

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com" rel="preconnect">
  <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,300;1,400;1,500;1,600;1,700;1,800&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">

  <!-- Vendor CSS Files -->
  <link href="{% static 'assets/vendor/bootstrap/css/bootstrap.min.css' %}" rel="stylesheet">
  <link href="{% static 'assets/vendor/bootstrap-icons/bootstrap-icons.css' %}" rel="stylesheet">
  <link href="{% static 'assets/vendor/aos/aos.css' %}" rel="stylesheet">
  <link href="{% static 'assets/vendor/fontawesome-free/css/all.min.css' %}" rel="stylesheet">
  <link href="{% static 'assets/vendor/glightbox/css/glightbox.min.css' %}" rel="stylesheet">
  <link href="{% static 'assets/vendor/swiper/swiper-bundle.min.css' %}" rel="stylesheet">

  <!-- Main CSS File -->
  <link href="{% static 'assets/css/main.css' %}" rel="stylesheet">

  <!-- =======================================================
  * Template Name: UpConstruction
  * Template URL: https://bootstrapmade.com/upconstruction-bootstrap-construction-website-template/
  * Updated: Aug 07 2024 with Bootstrap v5.3.3
  * Author: BootstrapMade.com
  * License: https://bootstrapmade.com/license/
  ======================================================== -->
</head>

<body class="{{ body_class }}">

  <header id="header" class="header d-flex align-items-center fixed-top">
    <div class="container-fluid container-xl position-relative d-flex align-items-center justify-content-between">

      <a href="{% url 'index' %}" class="logo d-flex align-items-center">
        {% if site_settings.logo %}
        <img src="{{ site_settings.logo.url }}" alt="{{ site_settings.site_name }}">
        {% endif %}
        <h1 class="sitename">{{ site_settings.site_name }}</h1> <span>.</span>
      </a>

      <nav id="navmenu" class="navmenu">
        <ul>
          <li><a href="{% url 'index' %}" {% if request.path == '/' %}class="active"{% endif %}>Home</a></li>
          <li><a href="{% url 'about' %}" {% if 'about' in request.path %}class="active"{% endif %}>About</a></li>
          <li><a href="{% url 'services' %}" {% if 'services' in request.path %}class="active"{% endif %}>Services</a></li>
          <li><a href="{% url 'projects' %}" {% if 'projects' in request.path %}class="active"{% endif %}>Projects</a></li>
          <li><a href="{% url 'blog' %}" {% if 'blog' in request.path %}class="active"{% endif %}>Blog</a></li>
          <li><a href="{% url 'contact' %}" {% if 'contact' in request.path %}class="active"{% endif %}>Contact</a></li>
        </ul>
        <i class="mobile-nav-toggle d-xl-none bi bi-list"></i>
      </nav>

    </div>
  </header>

  <main class="main">
    {% block content %}{% endblock %}
  </main>

  <footer id="footer" class="footer dark-background">

    <div class="container footer-top">
      <div class="row gy-4">
        <div class="col-lg-4 col-md-6 footer-about">
          <a href="{% url 'index' %}" class="logo d-flex align-items-center">
            <span class="sitename">{{ site_settings.site_name }}</span>
          </a>
          <p>{{ site_settings.site_description }}</p>
          <div class="footer-contact pt-3">
            <p>{{ site_settings.address }}</p>
            <p class="mt-3"><strong>Phone:</strong> <span>{{ site_settings.phone }}</span></p>
            <p><strong>Email:</strong> <span>{{ site_settings.email }}</span></p>
          </div>
          <div class="social-links d-flex mt-4">
            {% if site_settings.twitter_url %}<a href="{{ site_settings.twitter_url }}"><i class="bi bi-twitter-x"></i></a>{% endif %}
            {% if site_settings.facebook_url %}<a href="{{ site_settings.facebook_url }}"><i class="bi bi-facebook"></i></a>{% endif %}
            {% if site_settings.instagram_url %}<a href="{{ site_settings.instagram_url }}"><i class="bi bi-instagram"></i></a>{% endif %}
            {% if site_settings.linkedin_url %}<a href="{{ site_settings.linkedin_url }}"><i class="bi bi-linkedin"></i></a>{% endif %}
          </div>
        </div>

        <div class="col-lg-2 col-md-3 footer-links">
          <h4>Useful Links</h4>
          <ul>
            <li><a href="{% url 'index' %}">Home</a></li>
            <li><a href="{% url 'about' %}">About us</a></li>
            <li><a href="{% url 'services' %}">Services</a></li>
            <li><a href="{% url 'projects' %}">Projects</a></li>
            <li><a href="{% url 'blog' %}">Blog</a></li>
            <li><a href="{% url 'contact' %}">Contact</a></li>
          </ul>
        </div>

        <div class="col-lg-2 col-md-3 footer-links">
          <h4>Our Services</h4>
          <ul>
            {% for service in services|slice:":5" %}
            <li><a href="{% url 'service_detail' service.slug %}">{{ service.title }}</a></li>
            {% empty %}
            <li><a href="{% url 'services' %}">View All Services</a></li>
            {% endfor %}
          </ul>
        </div>

        <div class="col-lg-2 col-md-3 footer-links">
          <h4>Recent Projects</h4>
          <ul>
            {% for project in projects|slice:":5" %}
            <li><a href="{% url 'project_detail' project.slug %}">{{ project.title }}</a></li>
            {% empty %}
            <li><a href="{% url 'projects' %}">View All Projects</a></li>
            {% endfor %}
          </ul>
        </div>

        <div class="col-lg-2 col-md-3 footer-links">
          <h4>Recent Posts</h4>
          <ul>
            {% for post in recent_posts|slice:":5" %}
            <li><a href="{% url 'blog_detail' post.slug %}">{{ post.title }}</a></li>
            {% empty %}
            <li><a href="{% url 'blog' %}">View All Posts</a></li>
            {% endfor %}
          </ul>
        </div>

      </div>
    </div>

    <div class="container copyright text-center mt-4">
      <p>© <span>Copyright</span> <strong class="px-1 sitename">{{ site_settings.site_name }}</strong> <span>All Rights Reserved</span></p>
      <div class="credits">
        <!-- All the links in the footer should remain intact. -->
        <!-- You can delete the links only if you've purchased the pro version. -->
        <!-- Licensing information: https://bootstrapmade.com/license/ -->
        <!-- Purchase the pro version with working PHP/AJAX contact form: [buy-url] -->
        Design & Developed💙 | Imagine Infotech| नेपालमा बनेको
      </div>
    </div>

  </footer>

  <!-- Scroll Top -->
  <a href="#" id="scroll-top" class="scroll-top d-flex align-items-center justify-content-center"><i class="bi bi-arrow-up-short"></i></a>

  <!-- Preloader -->
  <div id="preloader"></div>

  <!-- Vendor JS Files -->
  <script src="{% static 'assets/vendor/bootstrap/js/bootstrap.bundle.min.js' %}"></script>
  <script src="{% static 'assets/vendor/php-email-form/validate.js' %}"></script>
  <script src="{% static 'assets/vendor/aos/aos.js' %}"></script>
  <script src="{% static 'assets/vendor/glightbox/js/glightbox.min.js' %}"></script>
  <script src="{% static 'assets/vendor/imagesloaded/imagesloaded.pkgd.min.js' %}"></script>
  <script src="{% static 'assets/vendor/isotope-layout/isotope.pkgd.min.js' %}"></script>
  <script src="{% static 'assets/vendor/swiper/swiper-bundle.min.js' %}"></script>
  <script src="{% static 'assets/vendor/purecounter/purecounter_vanilla.js' %}"></script>

  <!-- Main JS File -->
  <script src="{% static 'assets/js/main.js' %}"></script>

</body>

</html>