{% extends 'base.html' %}
{% load static %}

{% block content %}
  <!-- Hero Section -->
  <section id="hero" class="hero section relative gradient-primary text-white py-20 lg:py-32 overflow-hidden">
    <!-- Background Slider -->
    <div id="hero-carousel" class="carousel slide position-absolute top-0 start-0 w-100 h-100" data-bs-ride="carousel" data-bs-interval="5000">
      {% for i in '12345'|make_list %}
        <div class="carousel-item {% if forloop.first %}active{% endif %} position-relative">
          <img src="{% static 'img/hero-carousel/hero-carousel-'|add:i|add:'.jpg' %}" class="d-block w-100 h-100 object-fit-cover" alt="Hero Image {{ i }}" />
          <div class="position-absolute top-0 start-0 w-100 h-100 bg-dark opacity-50"></div>
        </div>
      {% endfor %}

      <!-- Carousel Controls -->
      <a class="carousel-control-prev" href="#hero-carousel" role="button" data-bs-slide="prev">
        <span class="carousel-control-prev-icon bi bi-chevron-left" aria-hidden="true"></span>
        <span class="visually-hidden">Previous</span>
      </a>
      <a class="carousel-control-next" href="#hero-carousel" role="button" data-bs-slide="next">
        <span class="carousel-control-next-icon bi bi-chevron-right" aria-hidden="true"></span>
        <span class="visually-hidden">Next</span>
      </a>

      <!-- Carousel Indicators -->
      <div class="carousel-indicators">
        {% for i in '12345'|make_list %}
          <button type="button" data-bs-target="#hero-carousel" data-bs-slide-to="{{ forloop.counter0 }}" {% if forloop.first %}class="active" aria-current="true"{% endif %} aria-label="Slide {{ forloop.counter }}"></button>
        {% endfor %}
      </div>
    </div>

    <!-- Hero Content -->
    <div class="info d-flex align-items-center position-relative" style="z-index: 10;">
      <div class="container">
        <div class="row justify-content-center" data-aos="fade-up" data-aos-delay="100">
          <div class="col-lg-8 text-center">
            <h1 class="display-3 fw-bold mb-4 text-white slide-up">
              Welcome to {{ site_settings.site_name }}
            </h1>
            <p class="lead mb-4 text-white-50 slide-up" data-aos="fade-up" data-aos-delay="200">
              {{ site_settings.site_description }}
            </p>
            <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center slide-up" data-aos="fade-up" data-aos-delay="300">
              <a href="#get-started" class="btn btn-light btn-lg px-5 py-3 rounded-pill fw-semibold">
                <i class="fas fa-rocket me-2"></i>Get Started
              </a>
              <a href="{% url 'about' %}" class="btn btn-outline-light btn-lg px-5 py-3 rounded-pill fw-semibold">
                <i class="fas fa-info-circle me-2"></i>Learn More
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  <!-- /Hero Section -->

  <!-- Get Started Section -->
  <section id="get-started" class="get-started section">
    <div class="container">
      <div class="row justify-content-between gy-4">
        <div class="col-lg-6 d-flex align-items-center" data-aos="zoom-out" data-aos-delay="100">
          <div class="content">
            <h3>{{ about_section.title }}</h3>
            <p>{{ about_section.description }}</p>
            {% if about_section.video_url %}
              <div class="video-wrapper mt-4">
                <a href="{{ about_section.video_url }}" class="glightbox play-btn"></a>
              </div>
            {% endif %}
          </div>
        </div>

        <div class="col-lg-5" data-aos="zoom-out" data-aos-delay="200">
          <form action="{% url 'contact' %}" method="post" class="php-email-form">
            {% csrf_token %}
            <h3>Get a quote</h3>
            <p>{{ site_settings.quote_description }}</p>
            <div class="row gy-3">
              <div class="col-12">
                <input type="text" name="name" class="form-control" placeholder="Name" required />
              </div>

              <div class="col-12">
                <input type="email" class="form-control" name="email" placeholder="Email" required />
              </div>

              <div class="col-12">
                <input type="text" class="form-control" name="subject" placeholder="Subject" required />
              </div>

              <div class="col-12">
                <textarea class="form-control" name="message" rows="6" placeholder="Message" required></textarea>
              </div>

              <div class="col-12 text-center">
                <div class="loading">Loading</div>
                <div class="error-message"></div>
                <div class="sent-message">Your quote request has been sent successfully. Thank you!</div>

                <button type="submit">Get a quote</button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </section>
  <!-- /Get Started Section -->

  <!-- Mission and Vision Section -->
  {% if about_section %}
  <section id="mission-vision" class="mission-vision section light-background">
    <div class="container section-title" data-aos="fade-up">
      <h2>Our Philosophy</h2>
      <p>The principles that guide our work and define our commitment to excellence</p>
    </div>
    <div class="container">
      <div class="row gy-4">
        <div class="col-lg-4" data-aos="fade-up">
          <div class="card h-100 mission-card text-center">
            <div class="card-body d-flex flex-column align-items-center">
              <div class="icon-box mb-4">
                <i class="bi bi-bullseye fs-1"></i>
              </div>
              <h4 class="card-title">{{ about_section.mission_title }}</h4>
              <p class="card-text">{{ about_section.mission_description }}</p>
            </div>
          </div>
        </div>
        <div class="col-lg-4" data-aos="fade-up" data-aos-delay="100">
          <div class="card h-100 vision-card text-center">
            <div class="card-body d-flex flex-column align-items-center">
              <div class="icon-box mb-4">
                <i class="bi bi-eye fs-1"></i>
              </div>
              <h4 class="card-title">{{ about_section.vision_title }}</h4>
              <p class="card-text">{{ about_section.vision_description }}</p>
            </div>
          </div>
        </div>
        <div class="col-lg-4" data-aos="fade-up" data-aos-delay="200">
          <div class="card h-100 values-card text-center">
            <div class="card-body d-flex flex-column align-items-center">
              <div class="icon-box mb-4">
                <i class="bi bi-heart fs-1"></i>
              </div>
              <h4 class="card-title">{{ about_section.values_title }}</h4>
              <p class="card-text">{{ about_section.values_description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  {% endif %}
  <!-- /Mission and Vision Section -->

  <!-- Services Section -->
  <section id="services" class="services section">
    <!-- Section Title -->
    <div class="container section-title" data-aos="fade-up">
      <h2>Services</h2>
      <p>{{ site_settings.services_description }}</p>
    </div>
    <!-- End Section Title -->

    <div class="container">
      <div class="row gy-4">
        {% for service in services %}
          <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="{{ forloop.counter }}00">
            <div class="service-item position-relative">
              <div class="icon">
                <i class="{{ service.icon }}"></i>
              </div>
              <h3>{{ service.title }}</h3>
              <p>{{ service.description }}</p>
              <a href="{% url 'service_detail' service.slug %}" class="readmore stretched-link">Read more <i class="bi bi-arrow-right"></i></a>
            </div>
          </div>
        {% endfor %}
      </div>
    </div>
  </section>
  <!-- /Services Section -->

  <!-- Projects Section -->
  <section id="projects" class="projects section light-background">
    <!-- Section Title -->
    <div class="container section-title" data-aos="fade-up">
      <h2>Projects</h2>
      <p>{{ site_settings.projects_description }}</p>
    </div>
    <!-- End Section Title -->

    <div class="container">
      <div class="isotope-layout" data-default-filter="*" data-layout="masonry" data-sort="original-order">
        <ul class="portfolio-filters isotope-filters" data-aos="fade-up" data-aos-delay="100">
          <li data-filter="*" class="filter-active">All</li>
          {% for category in project_categories %}
            <li data-filter=".filter-{{ category|lower }}">{{ category }}</li>
          {% endfor %}
        </ul>
        <!-- End Portfolio Filters -->

        <div class="row gy-4 isotope-container" data-aos="fade-up" data-aos-delay="200">
          {% for project in projects %}
            <div class="col-lg-4 col-md-6 portfolio-item isotope-item filter-{{ project.category|lower }}">
              <div class="portfolio-content h-100">
                <img src="{{ project.image.url }}" class="img-fluid" alt="{{ project.title }}" />
                <div class="portfolio-info">
                  <h4>{{ project.title }}</h4>
                  <p>{{ project.client }}</p>
                  <a href="{{ project.image.url }}" title="{{ project.title }}" data-gallery="portfolio-gallery-{{ project.category|lower }}" class="glightbox preview-link"><i class="bi bi-zoom-in"></i></a>
                  <a href="{% url 'project_detail' project.slug %}" title="More Details" class="details-link"><i class="bi bi-link-45deg"></i></a>
                </div>
              </div>
            </div>
          {% endfor %}
        </div>
      </div>
    </div>
  </section>
  <!-- /Projects Section -->

  <!-- Team Section -->
  <section id="team" class="team section">
    <div class="container section-title" data-aos="fade-up">
      <h2>Our Team</h2>
      <p>Meet our experienced professionals who make it all happen</p>
    </div>

    <div class="container">
      <div class="row gy-4">
        {% for member in team_members %}
          <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="{{ forloop.counter }}00">
            <div class="team-member">
              <div class="member-img">
                <img src="{{ member.image.url }}" class="img-fluid" alt="{{ member.name }}">
                <div class="social">
                  {% if member.twitter_url %}
                    <a href="{{ member.twitter_url }}"><i class="bi bi-twitter"></i></a>
                  {% endif %}
                  {% if member.facebook_url %}
                    <a href="{{ member.facebook_url }}"><i class="bi bi-facebook"></i></a>
                  {% endif %}
                  {% if member.instagram_url %}
                    <a href="{{ member.instagram_url }}"><i class="bi bi-instagram"></i></a>
                  {% endif %}
                  {% if member.linkedin_url %}
                    <a href="{{ member.linkedin_url }}"><i class="bi bi-linkedin"></i></a>
                  {% endif %}
                </div>
              </div>
              <div class="member-info text-center">
                <h4>{{ member.name }}</h4>
                <span>{{ member.position }}</span>
                <p>{{ member.bio|truncatewords:20 }}</p>
              </div>
            </div>
          </div>
        {% endfor %}
      </div>
    </div>
  </section>
  <!-- /Team Section -->

  <!-- Partnership Section -->
  <section id="partnerships" class="partnerships section">
    <div class="container section-title" data-aos="fade-up">
      <h2>Our Partners</h2>
      <p>Trusted partnerships that help us deliver excellence in every project</p>
    </div>

    <div class="container">
      <div class="row">
        <div class="col-12">
          <div class="swiper partnership-swiper" data-aos="fade-up" data-aos-delay="100">
            <div class="swiper-wrapper align-items-center">
              <!-- Partner logos - you can replace these with actual partner logos -->
              <div class="swiper-slide">
                <div class="partner-logo d-flex align-items-center justify-content-center">
                  <div class="logo-placeholder bg-light p-4 rounded shadow-sm">
                    <i class="fas fa-building text-primary" style="font-size: 3rem;"></i>
                    <div class="mt-2 text-center">
                      <small class="text-muted">Partner 1</small>
                    </div>
                  </div>
                </div>
              </div>
              <div class="swiper-slide">
                <div class="partner-logo d-flex align-items-center justify-content-center">
                  <div class="logo-placeholder bg-light p-4 rounded shadow-sm">
                    <i class="fas fa-industry text-primary" style="font-size: 3rem;"></i>
                    <div class="mt-2 text-center">
                      <small class="text-muted">Partner 2</small>
                    </div>
                  </div>
                </div>
              </div>
              <div class="swiper-slide">
                <div class="partner-logo d-flex align-items-center justify-content-center">
                  <div class="logo-placeholder bg-light p-4 rounded shadow-sm">
                    <i class="fas fa-handshake text-primary" style="font-size: 3rem;"></i>
                    <div class="mt-2 text-center">
                      <small class="text-muted">Partner 3</small>
                    </div>
                  </div>
                </div>
              </div>
              <div class="swiper-slide">
                <div class="partner-logo d-flex align-items-center justify-content-center">
                  <div class="logo-placeholder bg-light p-4 rounded shadow-sm">
                    <i class="fas fa-globe text-primary" style="font-size: 3rem;"></i>
                    <div class="mt-2 text-center">
                      <small class="text-muted">Partner 4</small>
                    </div>
                  </div>
                </div>
              </div>
              <div class="swiper-slide">
                <div class="partner-logo d-flex align-items-center justify-content-center">
                  <div class="logo-placeholder bg-light p-4 rounded shadow-sm">
                    <i class="fas fa-users text-primary" style="font-size: 3rem;"></i>
                    <div class="mt-2 text-center">
                      <small class="text-muted">Partner 5</small>
                    </div>
                  </div>
                </div>
              </div>
              <div class="swiper-slide">
                <div class="partner-logo d-flex align-items-center justify-content-center">
                  <div class="logo-placeholder bg-light p-4 rounded shadow-sm">
                    <i class="fas fa-award text-primary" style="font-size: 3rem;"></i>
                    <div class="mt-2 text-center">
                      <small class="text-muted">Partner 6</small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  <!-- /Partnership Section -->

  <!-- Recent Blog Posts Section -->
  <section id="recent-blog-posts" class="recent-blog-posts section light-background">
    <!-- Section Title -->
    <div class="container section-title" data-aos="fade-up">
      <h2>Recent Blog Posts</h2>
      <p>{{ site_settings.blog_description }}</p>
    </div>
    <!-- End Section Title -->

    <div class="container">
      <div class="row gy-5">
        {% for post in recent_posts %}
          <div class="col-xl-4 col-md-6">
            <div class="post-item position-relative h-100" data-aos="fade-up" data-aos-delay="{{ forloop.counter }}00">
              <div class="post-img position-relative overflow-hidden">
                <img src="{{ post.image.url }}" class="img-fluid" alt="{{ post.title }}" />
                <span class="post-date">{{ post.created_at|date:'F d' }}</span>
              </div>

              <div class="post-content d-flex flex-column">
                <h3 class="post-title">{{ post.title }}</h3>

                <div class="meta d-flex align-items-center">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-person"></i> <span class="ps-2">{{ post.author }}</span>
                  </div>
                  <span class="px-3 text-black-50">/</span>
                  <div class="d-flex align-items-center">
                    <i class="bi bi-folder2"></i> <span class="ps-2">{{ post.category }}</span>
                  </div>
                </div>

                <hr />

                <a href="{% url 'blog_detail' post.slug %}" class="readmore stretched-link"><span>Read More</span><i class="bi bi-arrow-right"></i></a>
              </div>
            </div>
          </div>
        {% endfor %}
      </div>
    </div>
  </section>
  <!-- /Recent Blog Posts Section -->

{% endblock %}
